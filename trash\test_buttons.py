#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web界面按钮功能
"""

import requests
import time
import json

API_BASE = "http://192.168.0.138:5000"

def get_status():
    """获取当前状态"""
    try:
        response = requests.post(f"{API_BASE}/api/status", timeout=5)
        data = response.json()
        return data
    except Exception as e:
        print(f"获取状态失败: {e}")
        return None

def start_detection():
    """启动检测"""
    try:
        response = requests.post(f"{API_BASE}/api/start", timeout=5)
        data = response.json()
        print(f"启动检测: {data['message']}")
        return True
    except Exception as e:
        print(f"启动检测失败: {e}")
        return False

def stop_detection():
    """停止检测"""
    try:
        response = requests.post(f"{API_BASE}/api/stop", timeout=5)
        data = response.json()
        print(f"停止检测: {data['message']}")
        return True
    except Exception as e:
        print(f"停止检测失败: {e}")
        return False

def print_status(status):
    """打印状态信息"""
    if not status:
        print("❌ 无法获取状态")
        return
    
    detection = "🟢 启用" if status['detection_enabled'] else "🔴 禁用"
    processing = "🟢 活跃" if status['processing_active'] else "🔴 停止"
    
    print(f"检测状态: {detection}")
    print(f"处理状态: {processing}")
    print(f"接收帧数: {status['stats']['frames_received']}")
    print(f"处理帧数: {status['stats']['frames_processed']}")
    print("-" * 40)

def main():
    print("🔧 测试Web界面按钮功能")
    print("=" * 50)
    
    # 1. 获取初始状态
    print("1. 获取初始状态:")
    initial_status = get_status()
    print_status(initial_status)
    
    # 2. 启动检测
    print("2. 启动检测:")
    if start_detection():
        time.sleep(2)  # 等待2秒
        status_after_start = get_status()
        print_status(status_after_start)
        
        # 检查是否真的启动了
        if status_after_start and status_after_start['detection_enabled']:
            print("✅ 检测成功启动")
        else:
            print("❌ 检测启动失败")
    
    # 3. 等待一段时间，观察帧数变化
    print("3. 等待5秒，观察帧数变化:")
    time.sleep(5)
    status_during = get_status()
    print_status(status_during)
    
    # 4. 停止检测
    print("4. 停止检测:")
    if stop_detection():
        time.sleep(2)  # 等待2秒
        status_after_stop = get_status()
        print_status(status_after_stop)
        
        # 检查是否真的停止了
        if status_after_stop and not status_after_stop['detection_enabled']:
            print("✅ 检测成功停止")
        else:
            print("❌ 检测停止失败")
    
    # 5. 最终验证
    print("5. 最终验证 - 再等待3秒:")
    time.sleep(3)
    final_status = get_status()
    print_status(final_status)
    
    # 分析结果
    print("\n📊 测试结果分析:")
    if initial_status and status_after_start and status_after_stop:
        frames_before = initial_status['stats']['frames_processed']
        frames_during = status_during['stats']['frames_processed'] if status_during else frames_before
        frames_after = final_status['stats']['frames_processed'] if final_status else frames_during
        
        print(f"启动前处理帧数: {frames_before}")
        print(f"运行中处理帧数: {frames_during}")
        print(f"停止后处理帧数: {frames_after}")
        
        if frames_during > frames_before:
            print("✅ 启动检测后帧数增长 - 按钮有效")
        else:
            print("❌ 启动检测后帧数未增长 - 按钮无效")
        
        if frames_after == frames_during or frames_after - frames_during < 10:
            print("✅ 停止检测后帧数停止增长 - 按钮有效")
        else:
            print("❌ 停止检测后帧数继续增长 - 按钮无效")

if __name__ == "__main__":
    main()
