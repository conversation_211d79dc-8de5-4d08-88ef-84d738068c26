import cv2
import torch
import numpy as np
from ultralytics import YOL<PERSON>
from threading import Thread
from queue import Queue, Empty
from time import time, sleep
import os
import warnings
import subprocess

import socket
import http.server
import io
import base64

# 环境配置抑制日志
os.environ['OPENCV_LOG_LEVEL'] = 'SILENT'
os.environ['FFMPEG_LOG_LEVEL'] = 'quiet'
warnings.filterwarnings('ignore')
cv2.setLogLevel(0)

# 设备检测和配置
device = 'cuda' if torch.cuda.is_available() else 'cpu'

# 打印环境信息
print("=" * 50)
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA device count: {torch.cuda.device_count()}")
    print(f"CUDA device name: {torch.cuda.get_device_name(0)}")
    print(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
print(f"Using device: {device}")
print(f"OpenCV version: {cv2.__version__}")
print("=" * 50)

# 模型初始化
MODEL_PATH = 'yolov8n.pt'
model = YOLO(MODEL_PATH)

# 将模型移动到指定设备
if device == 'cuda':
    model.to(device)
    print(f"[DEBUG] 模型已加载到GPU: {device}")
else:
    print(f"[DEBUG] 模型已加载到CPU: {device}")

print("[DEBUG] 模型加载成功")

# 类别映射
# garbage_classes = {39: 'bottle', 67: 'cell phone'}
garbage_classes ={0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane', 5: 'bus', 6: 'train', 7: 'truck', 8: 'boat', 9: 'traffic light', 10: 'fire hydrant', 11: 'stop sign', 12: 'parking meter', 13: 'bench', 14: 'bird', 15: 'cat', 16: 'dog', 17: 'horse', 18: 'sheep', 19: 'cow', 20: 'elephant', 21: 'bear', 22: 'zebra', 23: 'giraffe', 24: 'backpack', 25: 'umbrella', 26: 'handbag', 27: 'tie', 28: 'suitcase', 29: 'frisbee', 30: 'skis', 31: 'snowboard', 32: 'sports ball', 33: 'kite', 34: 'baseball bat', 35: 'baseball glove', 36: 'skateboard', 37: 'surfboard', 38: 'tennis racket', 39: 'bottle', 40: 'wine glass', 41: 'cup', 42: 'fork', 43: 'knife', 44: 'spoon', 45: 'bowl', 46: 'banana', 47: 'apple', 48: 'sandwich', 49: 'orange', 50: 'broccoli', 51: 'carrot', 52: 'hot dog', 53: 'pizza', 54: 'donut', 55: 'cake', 56: 'chair', 57: 'couch', 58: 'potted plant', 59: 'bed', 60: 'dining table', 61: 'toilet', 62: 'tv', 63: 'laptop', 64: 'mouse', 65: 'remote', 66: 'keyboard', 67: 'cell phone', 68: 'microwave', 69: 'oven', 70: 'toaster', 71: 'sink', 72: 'refrigerator', 73: 'book', 74: 'clock', 75: 'vase', 76: 'scissors', 77: 'teddy bear', 78: 'hair drier', 79: 'toothbrush'}
# 参数配置
frame_queue = Queue(maxsize=3)
results_queue = Queue(maxsize=3)
stream_queue = Queue(maxsize=2)  # 推流队列，更小的缓冲
CONFIDENCE_THRESHOLD = 0.5

# 全局变量用于HTTP流
current_frame = None
import threading
frame_lock = threading.Lock()  # 用于线程安全

# 统计信息
stats = {
    'frames_received': 0, 
    'frames_processed': 0, 
    'frames_displayed': 0, 
    'valid_frames': 0, 
    'error_frames': 0, 
    'queue_drops': 0, 
    'start_time': time()
}

class StreamingHandler(http.server.BaseHTTPRequestHandler):
    """HTTP流媒体处理器"""

    def do_GET(self):
        global current_frame

        if self.path == '/':
            # 主页面
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()

            html = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Local Camera Garbage Detection - Live Stream</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; background: #f0f0f0; }
                    .container { max-width: 800px; margin: 0 auto; padding: 20px; }
                    img { max-width: 100%; border: 2px solid #333; border-radius: 10px; }
                    .info { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🗑️ Local Camera Garbage Detection</h1>
                    <div class="info">
                        <p>Real-time Detection: bottle and cell phone</p>
                        <p>Access URL: http://""" + get_local_ip() + """:5000</p>
                    </div>
                    <img src="/video_feed" alt="Live Detection Stream">
                    <div class="info">
                        <p>💡 Tip: Page will auto-refresh to show latest detection results</p>
                    </div>
                </div>
            </body>
            </html>
            """
            self.wfile.write(html.encode('utf-8'))

        elif self.path == '/video_feed':
            # MJPEG流
            self.send_response(200)
            self.send_header('Content-Type', 'multipart/x-mixed-replace; boundary=frame')
            self.send_header('Cache-Control', 'no-cache')
            self.send_header('Connection', 'close')
            self.end_headers()

            frame_count = 0
            while True:
                try:
                    frame_to_send = None
                    with frame_lock:
                        if current_frame is not None:
                            frame_to_send = current_frame.copy()

                    # 如果没有帧，创建一个默认帧
                    if frame_to_send is None:
                        frame_to_send = create_test_frame()
                        cv2.putText(frame_to_send, f"HTTP Frame: {frame_count}", (20, 60),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 2)

                    # 编码为JPEG
                    ret, buffer = cv2.imencode('.jpg', frame_to_send,
                                             [cv2.IMWRITE_JPEG_QUALITY, 85])
                    if ret:
                        frame_bytes = buffer.tobytes()

                        # 发送MJPEG帧 - 修正格式
                        self.wfile.write(b'--frame\r\n')
                        self.wfile.write(b'Content-Type: image/jpeg\r\n')
                        self.wfile.write(f'Content-Length: {len(frame_bytes)}\r\n\r\n'.encode())
                        self.wfile.write(frame_bytes)
                        self.wfile.write(b'\r\n')
                        self.wfile.flush()

                        frame_count += 1

                    sleep(0.1)  # 控制帧率约10FPS

                except Exception as e:
                    print(f"[HTTP] 流传输错误: {e}")
                    break
        else:
            # 404
            self.send_response(404)
            self.end_headers()

    def log_message(self, format, *args):
        # 抑制HTTP服务器日志
        _ = format, args  # 避免未使用变量警告
        pass

def get_local_ip():
    """获取本机局域网IP地址"""
    try:
        # 连接到一个远程地址来获取本地IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.settimeout(2)  # 设置2秒超时
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception as e:
        print(f"[WARNING] 获取IP失败: {e}, 使用默认IP")
        return "*************"  # 使用已知的IP作为默认值

def is_frame_valid(frame):
    """检查帧是否有效"""
    if frame is None or frame.size == 0: 
        return False
    if len(frame.shape) != 3 or frame.shape[2] != 3: 
        return False
    if frame.shape[0] < 100 or frame.shape[1] < 100: 
        return False
    mean_val = np.mean(frame)
    if mean_val < 5 or mean_val > 250: 
        return False
    try:
        if np.std(cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)) < 10: 
            return False
    except:
        return False
    return True

def process_frames():
    """处理帧的线程函数"""
    print("[THREAD] 帧处理线程启动")
    while True:
        try:
            frame = frame_queue.get(timeout=5.0)
            if frame is None:
                print("[THREAD] 收到终止信号")
                break

            if not is_frame_valid(frame):
                stats['error_frames'] += 1
                continue

            stats['valid_frames'] += 1

            # YOLO检测 (使用配置的设备)
            results = model(frame, imgsz=416, conf=CONFIDENCE_THRESHOLD, verbose=False, device=device)[0]
            detections = []

            for box in results.boxes:
                class_id = int(box.cls)
                if class_id in garbage_classes:
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    # 过滤太小的检测框
                    if (x2 - x1) * (y2 - y1) > 500:
                        conf = float(box.conf)
                        label = f"{garbage_classes[class_id]} {conf:.2f}"
                        detections.append((x1, y1, x2, y2, label))

            stats['frames_processed'] += 1

            # 放入结果队列
            try:
                results_queue.put_nowait((frame, detections))
            except:
                # 队列满了，移除旧的结果
                try:
                    results_queue.get_nowait()
                    results_queue.put_nowait((frame, detections))
                except:
                    pass

        except Empty:
            print("[WARNING] 帧处理线程等待超时")
            continue
        except Exception as e:
            print(f"[ERROR] 帧处理异常: {e}")

def stream_frames(ffmpeg_proc):
    """推流线程函数 - 独立处理推流避免阻塞"""
    print("[STREAM_THREAD] 推流线程启动")
    if ffmpeg_proc is None:
        print("[STREAM_THREAD] FFmpeg进程为空，推流线程退出")
        return

    while True:
        try:
            frame_data = stream_queue.get(timeout=5.0)
            if frame_data is None:
                print("[STREAM_THREAD] 收到终止信号")
                break

            # 非阻塞写入
            try:
                ffmpeg_proc.stdin.write(frame_data)
                ffmpeg_proc.stdin.flush()
            except BrokenPipeError:
                print("[STREAM_THREAD] 推流管道断开")
                break
            except Exception as e:
                print(f"[STREAM_THREAD] 推流错误: {e}")
                break

        except Empty:
            continue
        except Exception as e:
            print(f"[STREAM_THREAD] 推流线程异常: {e}")
            break

    print("[STREAM_THREAD] 推流线程退出")

def start_http_server(port=5000):
    """启动HTTP流媒体服务器"""
    try:
        from http.server import HTTPServer
        # 绑定到所有接口，确保局域网可访问
        httpd = HTTPServer(("0.0.0.0", port), StreamingHandler)
        local_ip = get_local_ip()
        print(f"[HTTP] HTTP流媒体服务器启动")
        print(f"[HTTP] 本地访问: http://localhost:{port}")
        print(f"[HTTP] 局域网访问: http://{local_ip}:{port}")
        print(f"[HTTP] 直接视频流: http://{local_ip}:{port}/video_feed")

        # 设置服务器超时，避免无限阻塞
        httpd.timeout = 1.0

        # 启动服务器循环
        print("[HTTP] 服务器开始监听...")
        while True:
            try:
                httpd.handle_request()
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"[HTTP] 请求处理错误: {e}")
                continue

    except Exception as e:
        print(f"[HTTP] 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()

def draw_detections(frame, detections):
    """在帧上绘制检测结果"""
    for x1, y1, x2, y2, label in detections:
        # 绘制边框
        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        
        # 绘制标签背景
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
        cv2.rectangle(frame, (x1, y1 - label_size[1] - 10), 
                     (x1 + label_size[0], y1), (0, 255, 0), -1)
        
        # 绘制标签文字
        cv2.putText(frame, label, (x1, y1 - 5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    return frame

def create_test_frame():
    """创建测试帧"""
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    frame[:] = (50, 50, 50)  # 深灰色背景

    # 添加文字
    cv2.putText(frame, "Camera Not Available", (150, 200),
                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    cv2.putText(frame, "Showing Test Pattern", (170, 250),
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 255), 2)
    cv2.putText(frame, f"Time: {int(time())}", (250, 300),
                cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

    # 添加一些图形
    cv2.circle(frame, (320, 240), 50, (0, 0, 255), 3)
    cv2.rectangle(frame, (200, 350), (440, 420), (255, 0, 0), 2)

    return frame

def local_camera_reader(camera_id=0):
    """读取本地摄像头，如果失败则使用测试图像"""
    print(f"[CAMERA] 尝试打开摄像头 {camera_id}")

    # 首先尝试摄像头
    cap = cv2.VideoCapture(camera_id)

    # 尝试不同的后端
    backends = [cv2.CAP_DSHOW, cv2.CAP_MSMF, cv2.CAP_V4L2]
    camera_working = False

    for backend in backends:
        try:
            cap = cv2.VideoCapture(camera_id, backend)
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            cap.set(cv2.CAP_PROP_FPS, 15)  # 降低帧率
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

            if cap.isOpened():
                ret, test_frame = cap.read()
                if ret and test_frame is not None:
                    print(f"[CAMERA] 摄像头 {camera_id} 使用后端 {backend} 成功")
                    camera_working = True
                    break
                else:
                    cap.release()
            else:
                cap.release()
        except:
            if cap.isOpened():
                cap.release()
            continue

    if not camera_working:
        print(f"[WARNING] 摄像头 {camera_id} 无法正常工作，使用测试图像")
        cap = None

    frame_count = 0
    while True:
        if camera_working and cap is not None:
            ret, frame = cap.read()
            if ret and frame is not None:
                stats['frames_received'] += 1
                yield frame
                continue
            else:
                # 摄像头失败，切换到测试模式
                print("[WARNING] 摄像头读取失败，切换到测试模式")
                camera_working = False
                if cap:
                    cap.release()
                    cap = None

        # 使用测试图像
        frame_count += 1
        test_frame = create_test_frame()

        # 添加帧计数
        cv2.putText(test_frame, f"Frame: {frame_count}", (20, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

        stats['frames_received'] += 1
        yield test_frame
        sleep(0.1)  # 控制测试帧率

# FFmpeg推流功能已移除，改用HTTP流媒体

def print_stats():
    """打印统计信息"""
    elapsed = time() - stats['start_time']
    fps_received = stats['frames_received'] / elapsed if elapsed > 0 else 0
    fps_processed = stats['frames_processed'] / elapsed if elapsed > 0 else 0
    fps_displayed = stats['frames_displayed'] / elapsed if elapsed > 0 else 0
    valid_rate = (stats['valid_frames'] / max(stats['frames_received'], 1)) * 100

    print("\n" + "=" * 60)
    print("📊 运行统计")
    print("=" * 60)
    print(f"运行时间: {elapsed:.1f}秒")
    print(f"设备: {device}")

    # GPU信息
    if device == 'cuda' and torch.cuda.is_available():
        gpu_memory_used = torch.cuda.memory_allocated(0) / 1024**3
        gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / 1024**3
        gpu_utilization = (gpu_memory_used / gpu_memory_total) * 100
        print(f"GPU内存: {gpu_memory_used:.2f}GB / {gpu_memory_total:.1f}GB ({gpu_utilization:.1f}%)")

    print(f"收到帧数: {stats['frames_received']} ({fps_received:.1f} FPS)")
    print(f"有效帧数: {stats['valid_frames']} (有效率: {valid_rate:.1f}%)")
    print(f"处理帧数: {stats['frames_processed']} ({fps_processed:.1f} FPS)")
    print(f"显示帧数: {stats['frames_displayed']} ({fps_displayed:.1f} FPS)")
    print(f"队列丢弃: {stats['queue_drops']}")
    print("=" * 60 + "\n")

def main():
    """主函数"""
    global current_frame

    # 获取本机IP
    local_ip = get_local_ip()
    print(f"[INFO] 本机IP地址: {local_ip}")

    # 启动处理线程
    Thread(target=process_frames, daemon=True).start()
    print("[THREAD] 帧处理线程启动")

    # 启动HTTP流媒体服务器（后台线程）
    http_port = 5000
    http_thread = Thread(target=start_http_server, args=(http_port,))
    http_thread.daemon = True  # 设为daemon，随主程序退出
    http_thread.start()
    sleep(2)  # 等待服务器启动
    
    # 窗口设置
    window_name = f"Local Garbage Detection (PID: {os.getpid()})"
    cv2.destroyAllWindows()
    
    print(f"[MAIN] 启动本地摄像头检测")
    print(f"[MAIN] 窗口名称: {window_name}")
    print(f"[MAIN] � 运行设备: {device}")
    if device == 'cuda':
        print(f"[MAIN] 🎮 GPU加速: 已启用")
    else:
        print(f"[MAIN] 💻 CPU模式: 已启用")
    print(f"[MAIN] �💡 按 'q' 退出，按 's' 保存截图")
    print(f"[MAIN] � HTTP流媒体: http://{local_ip}:{http_port}")
    print(f"[MAIN] � 局域网访问: 在浏览器打开上述地址")
    
    # 主循环变量
    fps_counter = 0
    fps_timer = time()
    last_stats_time = time()
    camera_id = 0  # 默认摄像头ID
    
    try:
        for frame in local_camera_reader(camera_id):
            if frame is None:
                continue
            
            # 放入处理队列
            try:
                if frame_queue.full():
                    frame_queue.get_nowait()
                    stats['queue_drops'] += 1
                frame_queue.put_nowait(frame.copy())
            except Exception as e:
                print(f"[ERROR] 队列操作失败: {e}")
            
            # 获取处理结果
            try:
                if not results_queue.empty():
                    processed_frame, detections = results_queue.get_nowait()
                    frame_with_boxes = draw_detections(processed_frame.copy(), detections)
                    
                    # 更新全局帧用于HTTP流
                    with frame_lock:
                        current_frame = frame_with_boxes.copy()
                    
                    # 计算FPS
                    fps_counter += 1
                    current_time = time()
                    if current_time - fps_timer >= 1.0:
                        fps = fps_counter / (current_time - fps_timer)
                        cv2.putText(frame_with_boxes, f"FPS: {fps:.1f}", (10, 30),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                        fps_counter = 0
                        fps_timer = current_time
                    
                    # 显示检测信息
                    info_text = f"Objects: {len(detections)} | IP: {local_ip}"
                    cv2.putText(frame_with_boxes, info_text, (10, 60),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
                    
                    stats['frames_displayed'] += 1
                    cv2.imshow(window_name, frame_with_boxes)
                    
                    # 如果有检测到目标，打印信息
                    if detections:
                        print(f"[DETECT] 检测到 {len(detections)} 个目标")
            
            except Empty:
                pass
            except Exception as e:
                print(f"[ERROR] 结果处理异常: {e}")
            
            # 按键处理
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print("[MAIN] 用户请求退出")
                break
            elif key == ord('s'):
                filename = f"local_detection_{int(time())}.jpg"
                cv2.imwrite(filename, frame)
                print(f"[MAIN] 💾 保存截图: {filename}")
            
            # 定期打印统计
            if time() - last_stats_time > 20:
                print_stats()
                last_stats_time = time()
    
    except KeyboardInterrupt:
        print("[MAIN] 程序被用户中断")
    except Exception as e:
        print(f"[ERROR] 主循环异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("[MAIN] 退出清理...")
        
        # 终止处理线程
        frame_queue.put(None)

        # 清理全局帧
        with frame_lock:
            current_frame = None
        
        # 关闭窗口
        cv2.destroyAllWindows()
        
        # 最终统计
        print_stats()
        print("[MAIN] 程序退出")

if __name__ == "__main__":
    main()
