#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import cv2
import platform
import sys

class Capture():
    '''获取摄像头信息并处理'''
    def __init__(self,camera_id=-1):
        self.camera_id=camera_id
        self.camera_working=False
        pass
        
    def find_camera(self):
        '''找到可用的摄像头和端口号'''

        print(f"👀 [Capture] 寻找可用的摄像头和端口号")
        if self.camera_id==-1:
            camera_id_list=range(0,11)
        else:
            camera_id_list=[]
            camera_id_list.append(self.camera_id)
        # 尝试不同的设备号
        for self.camera_id in camera_id_list:
            # 首先尝试摄像头
            try:
                cap = cv2.VideoCapture(self.camera_id)
            except IndentationError:
                print(f"❌ [Capture] 尝试打开摄像头 {self.camera_id} 失败")
            else:

                # 优先检测平台，再选择对应的后端列表
                system = platform.system()
                if system == "Windows":
                    backends = [cv2.CAP_DSHOW, cv2.CAP_MSMF, cv2.CAP_ANY]
                elif system == "Linux":
                    backends = [cv2.CAP_V4L2, cv2.CAP_FFMPEG, cv2.CAP_GSTREAMER]
                else:  # macOS
                    backends = [cv2.CAP_AVFOUNDATION, cv2.CAP_ANY]

                # 检测后端列表
                for backend in backends:
                    try:
                        # 设置相机参数
                        cap = cv2.VideoCapture(self.camera_id, backend)
                        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                        cap.set(cv2.CAP_PROP_FPS, 15)  # 降低帧率
                        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

                        # 尝试读一帧
                        ret, test_frame = cap.read()
                        if ret and test_frame is not None:
                            print(f"✅ [Capture] 摄像头 {self.camera_id} 使用后端 {backend} 成功")
                            self.camera_working = True
                            break
                        else:
                            cap.release()

                    except:
                        if cap.isOpened():
                            cap.release()
                        continue

                break


def main():
    print("⚙️ [main] 初始化程序开始运行")
    
    # print(cv2.__version__)

    capture=Capture()
    capture.find_camera()
    if not capture.camera_working:
        print("❌ 请检查摄像头连接,程序停止运行!")
        sys.exit(0)  # 0 表示正常退出

if __name__=="__main__":
    main()
