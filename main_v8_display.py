#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
main_v8_display.py - RTSP流显示客户端
连接到main_v7_all.py的HTTP MJPEG流，保存帧并提供控制功能
"""

import requests
import cv2
import numpy as np
import time
import threading
from threading import Lock
import os

# 抑制OpenCV日志
os.environ['OPENCV_LOG_LEVEL'] = 'SILENT'
cv2.setLogLevel(0)

class RTSPDisplayClient:
    def __init__(self):
        # 配置
        self.stream_url = "http://192.168.0.138:5000/video_feed"
        self.api_base_url = "http://192.168.0.138:5000/api"
        
        # 状态变量
        self.running = False
        self.current_frame = None
        self.frame_lock = Lock()
        
        # 统计信息
        self.stats = {
            'frames_received': 0,
            'frames_saved': 0,
            'connection_errors': 0,
            'start_time': time.time()
        }
        
        # 创建输出目录
        self.output_dir = "captured_frames"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
        
        print("🎥 RTSP流显示客户端初始化完成")
        print(f"📺 目标流地址: {self.stream_url}")
        print(f"🔧 API控制地址: {self.api_base_url}")
        print(f"📁 帧保存目录: {self.output_dir}")
    
    def test_connection(self):
        """测试连接"""
        try:
            # 测试API
            response = requests.post(f"{self.api_base_url}/status", timeout=3)
            if response.status_code == 200:
                data = response.json()
                print("✅ API连接成功")
                print(f"   检测状态: {'启用' if data['detection_enabled'] else '禁用'}")
                print(f"   接收帧数: {data['stats']['frames_received']}")
                print(f"   处理帧数: {data['stats']['frames_processed']}")
            else:
                print(f"❌ API连接失败: {response.status_code}")
                return False
            
            # 测试流
            response = requests.get(self.stream_url, stream=True, timeout=3)
            if response.status_code == 200:
                print("✅ 流连接成功")
                return True
            else:
                print(f"❌ 流连接失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 连接测试失败: {e}")
            return False
    
    def control_detection(self, action):
        """控制检测启停"""
        try:
            endpoint = f"{self.api_base_url}/{action}"
            response = requests.post(endpoint, timeout=3)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {action.upper()}命令成功: {data['message']}")
                return True
            else:
                print(f"❌ {action.upper()}命令失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ {action.upper()}命令异常: {e}")
            return False
    
    def save_frame(self, frame, frame_number):
        """保存帧到文件"""
        try:
            # 添加信息到帧上
            h, w = frame.shape[:2]
            
            # 添加时间戳和帧号
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            cv2.putText(frame, f"Frame: {frame_number}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 255), 2)
            cv2.putText(frame, f"Time: {timestamp}", (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            cv2.putText(frame, f"Size: {w}x{h}", (10, 90), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
            
            # 保存文件
            filename = f"{self.output_dir}/frame_{frame_number:06d}.jpg"
            success = cv2.imwrite(filename, frame)
            
            if success:
                self.stats['frames_saved'] += 1
                if frame_number % 30 == 0:  # 每30帧打印一次
                    print(f"💾 保存帧: {filename}")
                return True
            else:
                print(f"❌ 保存失败: {filename}")
                return False
                
        except Exception as e:
            print(f"❌ 保存异常: {e}")
            return False
    
    def stream_capture_thread(self):
        """流捕获线程"""
        print("🚀 启动流捕获线程...")
        
        while self.running:
            try:
                # 连接流
                response = requests.get(self.stream_url, stream=True, timeout=5)
                
                if response.status_code != 200:
                    print(f"❌ 流连接失败: {response.status_code}")
                    self.stats['connection_errors'] += 1
                    time.sleep(2)
                    continue
                
                print("✅ 流连接成功，开始捕获帧...")
                
                # 解析MJPEG流
                bytes_data = bytes()
                for chunk in response.iter_content(chunk_size=1024):
                    if not self.running:
                        break
                    
                    bytes_data += chunk
                    
                    # 查找JPEG帧边界
                    a = bytes_data.find(b'\xff\xd8')  # JPEG开始
                    b = bytes_data.find(b'\xff\xd9')  # JPEG结束
                    
                    if a != -1 and b != -1:
                        jpg = bytes_data[a:b+2]
                        bytes_data = bytes_data[b+2:]
                        
                        # 解码JPEG
                        frame = cv2.imdecode(np.frombuffer(jpg, dtype=np.uint8), cv2.IMREAD_COLOR)
                        
                        if frame is not None:
                            self.stats['frames_received'] += 1
                            
                            # 保存帧
                            self.save_frame(frame, self.stats['frames_received'])
                            
                            # 更新当前帧
                            with self.frame_lock:
                                self.current_frame = frame.copy()
                            
                            # 每100帧打印统计
                            if self.stats['frames_received'] % 100 == 0:
                                elapsed = time.time() - self.stats['start_time']
                                fps = self.stats['frames_received'] / elapsed if elapsed > 0 else 0
                                print(f"📊 接收: {self.stats['frames_received']} 帧, "
                                      f"保存: {self.stats['frames_saved']} 帧, "
                                      f"FPS: {fps:.1f}")
                
            except requests.exceptions.Timeout:
                print("⏰ 流连接超时，重试中...")
                self.stats['connection_errors'] += 1
                time.sleep(2)
            except requests.exceptions.ConnectionError:
                print("🔌 流连接断开，重试中...")
                self.stats['connection_errors'] += 1
                time.sleep(2)
            except Exception as e:
                print(f"❌ 流捕获异常: {e}")
                self.stats['connection_errors'] += 1
                time.sleep(2)
        
        print("🛑 流捕获线程结束")
    
    def interactive_control(self):
        """交互式控制"""
        print("\n" + "="*60)
        print("🎮 交互式控制界面")
        print("="*60)
        print("命令:")
        print("  s - 启动检测")
        print("  t - 停止检测")
        print("  i - 显示统计信息")
        print("  q - 退出程序")
        print("="*60)
        
        while self.running:
            try:
                command = input("请输入命令 (s/t/i/q): ").strip().lower()
                
                if command == 'q':
                    print("🛑 用户选择退出")
                    self.running = False
                    break
                elif command == 's':
                    print("🚀 启动检测...")
                    self.control_detection('start')
                elif command == 't':
                    print("🛑 停止检测...")
                    self.control_detection('stop')
                elif command == 'i':
                    self.show_stats()
                else:
                    print("❌ 无效命令，请输入 s/t/i/q")
                    
            except KeyboardInterrupt:
                print("\n🛑 收到中断信号，退出程序")
                self.running = False
                break
            except EOFError:
                print("\n🛑 输入结束，退出程序")
                self.running = False
                break
    
    def show_stats(self):
        """显示统计信息"""
        elapsed = time.time() - self.stats['start_time']
        fps = self.stats['frames_received'] / elapsed if elapsed > 0 else 0
        
        print("\n" + "="*50)
        print("📊 实时统计信息")
        print("="*50)
        print(f"运行时间: {elapsed:.1f}秒")
        print(f"接收帧数: {self.stats['frames_received']}")
        print(f"保存帧数: {self.stats['frames_saved']}")
        print(f"连接错误: {self.stats['connection_errors']}")
        print(f"平均FPS: {fps:.1f}")
        print(f"保存目录: {self.output_dir}")
        print("="*50 + "\n")
    
    def run(self):
        """运行客户端"""
        print("\n" + "="*60)
        print("🎥 RTSP流显示客户端启动")
        print("="*60)
        
        # 测试连接
        if not self.test_connection():
            print("❌ 连接测试失败，请确保main_v7_all.py正在运行")
            return
        
        self.running = True
        
        # 启动流捕获线程
        capture_thread = threading.Thread(target=self.stream_capture_thread, daemon=True)
        capture_thread.start()
        
        # 等待一下让流开始
        time.sleep(2)
        
        # 启动交互式控制
        try:
            self.interactive_control()
        except Exception as e:
            print(f"❌ 控制异常: {e}")
        
        self.running = False
        
        # 等待线程结束
        capture_thread.join(timeout=3)
        
        # 显示最终统计
        self.show_stats()
        print("🎉 程序结束")

def main():
    """主函数"""
    print("🎥 main_v8_display.py - RTSP流显示客户端")
    print("📺 连接到main_v7_all.py的HTTP MJPEG流")
    print("💾 自动保存帧到本地文件")
    print("🎮 提供交互式控制功能")
    
    client = RTSPDisplayClient()
    client.run()

if __name__ == "__main__":
    main()
