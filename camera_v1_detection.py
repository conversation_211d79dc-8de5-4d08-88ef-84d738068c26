import cv2
import torch
import numpy as np
from ultralytics import YOL<PERSON>
from threading import Thread
from queue import Queue, Empty
from time import time, sleep
import sys

# 打印环境信息
print("=" * 50)
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"OpenCV version: {cv2.__version__}")
print("=" * 50)

# 初始化YOLOv8模型
MODEL_PATH = 'yolov8n.pt'
print(f"[DEBUG] 正在加载模型: {MODEL_PATH}")
try:
    model = YOLO(MODEL_PATH)
    print("[DEBUG] 模型加载成功！")
except Exception as e:
    print(f"[ERROR] 模型加载失败: {e}")
    sys.exit(1)

# 定义要检测的垃圾类别ID（COCO数据集类别映射）
GARBAGE_CLASSES = {
    39: 'bottle',  # 瓶子
    67: 'cell phone'  # 纸巾等（使用相近类别）
}
print(f"[DEBUG] 检测类别配置: {GARBAGE_CLASSES}")

# 性能优化参数
FRAME_BUFFER_SIZE = 3  # 帧缓冲队列大小
CONFIDENCE_THRESHOLD = 0.5  # 置信度阈值
print(f"[CONFIG] 队列大小: {FRAME_BUFFER_SIZE}, 置信度阈值: {CONFIDENCE_THRESHOLD}")

# 多线程处理队列
frame_queue = Queue(maxsize=FRAME_BUFFER_SIZE)
results_queue = Queue(maxsize=FRAME_BUFFER_SIZE)

# 调试统计
stats = {
    'frames_received': 0,
    'frames_processed': 0,
    'frames_displayed': 0,
    'queue_drops': 0,
    'start_time': time()
}


def process_frames():
    """多线程处理帧"""
    print("[THREAD] 帧处理线程启动")
    while True:
        try:
            # 获取帧，设置超时避免永久阻塞
            frame = frame_queue.get(timeout=5.0)
            if frame is None:  # 终止信号
                print("[THREAD] 收到终止信号，退出处理线程")
                break

            # 记录处理开始时间
            process_start = time()

            # 使用YOLOv8进行检测
            results = model(frame, imgsz=640, conf=CONFIDENCE_THRESHOLD, verbose=False)[0]

            # 提取检测结果
            detections = []
            for box in results.boxes:
                class_id = int(box.cls)
                if class_id in GARBAGE_CLASSES:
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    conf = float(box.conf)
                    label = f"{GARBAGE_CLASSES[class_id]} {conf:.2f}"
                    detections.append((x1, y1, x2, y2, label))

            # 统计处理帧数
            stats['frames_processed'] += 1

            # 放入结果队列
            results_queue.put((frame, detections))

            # 打印处理耗时
            process_time = time() - process_start
            print(f"[PROCESS] 帧处理耗时: {process_time:.3f}s, 检测到目标: {len(detections)}个")

        except Empty:
            print("[WARNING] 帧处理线程等待超时，继续等待...")
            continue
        except Exception as e:
            print(f"[ERROR] 帧处理异常: {e}")
            continue


def draw_detections(frame, detections):
    """在帧上绘制检测框"""
    for (x1, y1, x2, y2, label) in detections:
        # 绘制矩形框
        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        # 添加标签
        cv2.putText(frame, label, (x1, y1 - 10),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
    return frame


def print_stats():
    """打印运行统计信息"""
    elapsed = time() - stats['start_time']
    fps_received = stats['frames_received'] / elapsed if elapsed > 0 else 0
    fps_processed = stats['frames_processed'] / elapsed if elapsed > 0 else 0
    fps_displayed = stats['frames_displayed'] / elapsed if elapsed > 0 else 0

    print("\n" + "=" * 50)
    print(f"运行时间: {elapsed:.1f}秒")
    print(f"收到帧数: {stats['frames_received']} ({fps_received:.1f} FPS)")
    print(f"处理帧数: {stats['frames_processed']} ({fps_processed:.1f} FPS)")
    print(f"显示帧数: {stats['frames_displayed']} ({fps_displayed:.1f} FPS)")
    print(f"队列丢弃帧数: {stats['queue_drops']}")
    print("=" * 50 + "\n")


# 启动处理线程
print("[MAIN] 启动处理线程")
processing_thread = Thread(target=process_frames)
processing_thread.daemon = True
processing_thread.start()

# 主处理循环
camera_id = 0  # 通常0表示默认摄像头，如果有多个摄像头可以尝试1,2等
print(f"[MAIN] 启动主循环，视频源: 摄像头ID {camera_id}")
fps_counter = 0
fps_timer = time()
last_stat_time = time()

# 创建摄像头捕获对象
cap = cv2.VideoCapture(camera_id)
if not cap.isOpened():
    print(f"[ERROR] 无法打开摄像头 {camera_id}")
    sys.exit(1)
print("[INFO] 摄像头连接成功")

try:
    while True:
        # 读取一帧
        ret, frame = cap.read()
        if not ret:
            print("[WARNING] 无法获取帧，跳过")
            continue
            
        # 更新统计信息
        stats['frames_received'] += 1

        # 尝试放入处理队列（非阻塞方式）
        try:
            # 如果队列满，尝试移除旧帧
            if frame_queue.full():
                try:
                    frame_queue.get_nowait()  # 丢弃一帧
                    stats['queue_drops'] += 1
                    print("[QUEUE] 队列已满，丢弃一帧")
                except Empty:
                    pass

            # 放入当前帧
            frame_queue.put_nowait(frame.copy())
            print(f"[QUEUE] 成功放入帧 (队列大小: {frame_queue.qsize()}/{FRAME_BUFFER_SIZE})")
        except Exception as e:
            print(f"[ERROR] 放入队列失败: {e}")

        # 尝试从结果队列获取处理好的帧（非阻塞）
        try:
            processed_frame, detections = results_queue.get_nowait()
            frame_with_boxes = draw_detections(processed_frame, detections)

            # 计算并显示FPS
            fps_counter += 1
            current_time = time()
            if current_time - fps_timer >= 1.0:
                fps = fps_counter / (current_time - fps_timer)
                cv2.putText(frame_with_boxes, f"FPS: {fps:.1f}", (10, 30),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                fps_counter = 0
                fps_timer = current_time

            # 更新显示帧数统计
            stats['frames_displayed'] += 1

            # 显示实时结果
            cv2.imshow('Garbage Detection (YOLOv8)', frame_with_boxes)
        except Empty:
            # 如果结果队列为空，显示原始帧（可选）
            cv2.imshow('Garbage Detection (YOLOv8)', frame)
            pass  # 结果队列为空是正常现象，无需处理
        except Exception as e:
            print(f"[ERROR] 结果处理异常: {e}")

        # 按'q'退出
        if cv2.waitKey(1) & 0xFF == ord('q'):
            print("[MAIN] 用户请求退出")
            break

        # 每隔10秒打印一次统计信息
        if time() - last_stat_time > 10:
            print_stats()
            last_stat_time = time()

except KeyboardInterrupt:
    print("[MAIN] 程序被用户中断")
except Exception as e:
    print(f"[ERROR] 主循环异常: {e}")
    import traceback
    traceback.print_exc()
finally:
    print("[MAIN] 清理资源...")
    # 发送终止信号
    frame_queue.put(None)
    # 等待线程结束
    processing_thread.join(timeout=5.0)
    if processing_thread.is_alive():
        print("[WARNING] 处理线程未正常退出")
    else:
        print("[INFO] 处理线程已退出")

    # 释放摄像头
    cap.release()
    
    # 打印最终统计
    print_stats()

    # 关闭窗口
    cv2.destroyAllWindows()
    print("[MAIN] 程序退出")