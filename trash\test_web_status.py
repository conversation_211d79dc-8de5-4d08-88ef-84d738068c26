#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web界面状态更新功能
"""

import requests
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

def test_web_interface():
    """测试Web界面状态更新"""
    print("🌐 测试Web界面状态更新功能")
    print("=" * 50)
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 无头模式
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    try:
        # 启动浏览器
        print("🚀 启动浏览器...")
        driver = webdriver.Chrome(options=chrome_options)
        driver.get("http://*************:5000")
        
        # 等待页面加载
        wait = WebDriverWait(driver, 10)
        
        # 等待状态元素加载
        print("⏳ 等待页面加载...")
        status_element = wait.until(
            EC.presence_of_element_located((By.CLASS_NAME, "status"))
        )
        
        # 检查初始状态
        print("1️⃣ 检查初始状态...")
        time.sleep(2)  # 等待JavaScript更新状态
        initial_status = status_element.text
        print(f"  初始状态: {initial_status}")
        
        # 点击启动按钮
        print("2️⃣ 点击启动检测按钮...")
        start_button = driver.find_element(By.XPATH, "//button[contains(text(), 'Start Detection')]")
        start_button.click()
        
        # 等待状态更新
        print("⏳ 等待状态更新...")
        time.sleep(3)  # 等待API调用和状态更新
        
        # 检查启动后状态
        updated_status = status_element.text
        print(f"  启动后状态: {updated_status}")
        
        # 验证状态是否改变
        if "Active" in updated_status:
            print("✅ 启动后状态更新成功")
        else:
            print("❌ 启动后状态未更新")
        
        # 点击停止按钮
        print("3️⃣ 点击停止检测按钮...")
        stop_button = driver.find_element(By.XPATH, "//button[contains(text(), 'Stop Detection')]")
        stop_button.click()
        
        # 等待状态更新
        time.sleep(3)
        
        # 检查停止后状态
        final_status = status_element.text
        print(f"  停止后状态: {final_status}")
        
        # 验证状态是否改变
        if "Inactive" in final_status:
            print("✅ 停止后状态更新成功")
        else:
            print("❌ 停止后状态未更新")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"❌ Web界面测试失败: {e}")
        try:
            driver.quit()
        except:
            pass
        return False

def test_api_directly():
    """直接测试API功能"""
    print("\n🔧 直接测试API功能")
    print("=" * 30)
    
    try:
        # 获取初始状态
        response = requests.post("http://*************:5000/api/status", timeout=5)
        initial_data = response.json()
        print(f"初始检测状态: {'Active' if initial_data['detection_enabled'] else 'Inactive'}")
        
        # 启动检测
        response = requests.post("http://*************:5000/api/start", timeout=5)
        start_result = response.json()
        print(f"启动结果: {start_result['message']}")
        
        # 检查启动后状态
        time.sleep(1)
        response = requests.post("http://*************:5000/api/status", timeout=5)
        after_start_data = response.json()
        print(f"启动后检测状态: {'Active' if after_start_data['detection_enabled'] else 'Inactive'}")
        
        # 停止检测
        response = requests.post("http://*************:5000/api/stop", timeout=5)
        stop_result = response.json()
        print(f"停止结果: {stop_result['message']}")
        
        # 检查停止后状态
        time.sleep(1)
        response = requests.post("http://*************:5000/api/status", timeout=5)
        after_stop_data = response.json()
        print(f"停止后检测状态: {'Active' if after_stop_data['detection_enabled'] else 'Inactive'}")
        
        return True
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 Web界面状态更新测试")
    print("=" * 60)
    
    # 先测试API功能
    api_success = test_api_directly()
    
    if not api_success:
        print("❌ API功能异常，跳过Web界面测试")
        return
    
    # 尝试测试Web界面
    try:
        web_success = test_web_interface()
        
        if web_success:
            print("\n🎉 Web界面状态更新测试通过！")
        else:
            print("\n❌ Web界面状态更新测试失败！")
            
    except ImportError:
        print("\n⚠️ 未安装selenium，跳过Web界面自动化测试")
        print("💡 请手动打开浏览器测试: http://*************:5000")
        print("   1. 点击 'Start Detection' 按钮")
        print("   2. 观察状态是否从 'Inactive' 变为 'Active'")
        print("   3. 点击 'Stop Detection' 按钮")
        print("   4. 观察状态是否从 'Active' 变为 'Inactive'")

if __name__ == "__main__":
    main()
