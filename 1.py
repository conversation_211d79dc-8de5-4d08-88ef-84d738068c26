import cv2

def list_cameras(max_tested=5):
    print("🔍 正在扫描可用摄像头...")
    available = []
    for i in range(max_tested):
        cap = cv2.VideoCapture(i)
        if cap is not None and cap.isOpened():
            print(f"✅ 摄像头索引 {i} 可用")
            available.append(i)
            cap.release()
        else:
            print(f"❌ 摄像头索引 {i} 不可用")
    return available

def preview_camera(index):
    print(f"\n🎥 正在打开摄像头 {index} ...")
    cap = cv2.VideoCapture(index)
    if not cap.isOpened():
        print("❌ 无法打开摄像头")
        return

    # 设置分辨率（可选）
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

    print("📋 摄像头信息:")
    print(f"分辨率: {cap.get(cv2.CAP_PROP_FRAME_WIDTH)} x {cap.get(cv2.CAP_PROP_FRAME_HEIGHT)}")
    print(f"FPS: {cap.get(cv2.CAP_PROP_FPS)}")

    print("🟢 按下 'q' 键退出预览")
    while True:
        ret, frame = cap.read()
        if not ret:
            print("⚠️ 无法读取帧")
            break
        cv2.imshow(f"Camera {index}", frame)
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    cams = list_cameras()
    if not cams:
        print("🚫 没有检测到任何可用摄像头")
    else:
        preview_camera(cams[0])
