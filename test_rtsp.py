#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_rtsp.py - 测试v8.py的RTSP流画面
检测v8.py系统的视频流输出和API状态
"""

import cv2
import requests
import numpy as np
import time
import threading
from threading import Lock
import os

# 抑制OpenCV日志
os.environ['OPENCV_LOG_LEVEL'] = 'SILENT'
cv2.setLogLevel(0)

class V8StreamTester:
    def __init__(self):
        # v8.py服务器配置
        self.api_base = "http://192.168.0.138:5000/api"
        self.web_url = "http://192.168.0.138:5000"
        
        # 测试配置
        self.window_name = "V8 Stream Test"
        self.running = False
        self.current_frame = None
        self.frame_lock = Lock()
        
        # 统计信息
        self.test_stats = {
            'api_calls': 0,
            'api_errors': 0,
            'frames_captured': 0,
            'test_start_time': time.time()
        }
        
        print("🧪 V8流画面测试器初始化完成")
        print(f"🎯 目标服务器: {self.web_url}")
        print(f"🔧 API地址: {self.api_base}")
    
    def test_api_connection(self):
        """测试API连接和状态"""
        try:
            print("\n📡 测试API连接...")
            response = requests.post(f"{self.api_base}/status", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                print("✅ API连接成功")
                
                # 显示系统信息
                sys_info = data['system_info']
                print(f"   设备: {sys_info['device'].upper()}")
                print(f"   模型: {'✅ 已加载' if sys_info['model_loaded'] else '❌ 未加载'}")
                print(f"   RTSP: {'✅ 连接' if sys_info['rtsp_connected'] else '❌ 断开'}")
                print(f"   本地IP: {sys_info['local_ip']}")
                
                # 显示检测信息
                stats = data['stats']
                print(f"   检测状态: {'🟢 启用' if data['detection_enabled'] else '🔴 禁用'}")
                print(f"   接收帧数: {stats['frames_received']}")
                print(f"   处理帧数: {stats['frames_processed']}")
                print(f"   接收FPS: {stats['fps_received']:.1f}")
                print(f"   处理FPS: {stats['fps_processed']:.1f}")
                
                self.test_stats['api_calls'] += 1
                return True
            else:
                print(f"❌ API连接失败: HTTP {response.status_code}")
                self.test_stats['api_errors'] += 1
                return False
                
        except Exception as e:
            print(f"❌ API连接异常: {e}")
            self.test_stats['api_errors'] += 1
            return False
    
    def control_detection(self, action):
        """控制检测启停"""
        try:
            print(f"\n🎮 {action.upper()}检测...")
            endpoint = f"{self.api_base}/{action}"
            response = requests.post(endpoint, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {action.upper()}成功: {data['message']}")
                self.test_stats['api_calls'] += 1
                return True
            else:
                print(f"❌ {action.upper()}失败: HTTP {response.status_code}")
                self.test_stats['api_errors'] += 1
                return False
                
        except Exception as e:
            print(f"❌ {action.upper()}异常: {e}")
            self.test_stats['api_errors'] += 1
            return False
    
    def capture_test_frames(self, num_frames=5):
        """捕获测试帧（模拟从v8.py获取画面）"""
        print(f"\n📸 模拟捕获 {num_frames} 帧画面...")
        
        for i in range(num_frames):
            try:
                # 模拟从v8.py获取当前处理状态
                response = requests.post(f"{self.api_base}/status", timeout=3)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # 创建模拟帧，显示v8.py的状态信息
                    frame = self.create_status_frame(data, i + 1)
                    
                    # 保存帧
                    filename = f"v8_test_frame_{i+1:02d}.jpg"
                    cv2.imwrite(filename, frame)
                    print(f"   📁 保存: {filename}")
                    
                    with self.frame_lock:
                        self.current_frame = frame.copy()
                    
                    self.test_stats['frames_captured'] += 1
                    self.test_stats['api_calls'] += 1
                    
                    time.sleep(1)  # 间隔1秒
                else:
                    print(f"   ❌ 状态获取失败: HTTP {response.status_code}")
                    self.test_stats['api_errors'] += 1
                    
            except Exception as e:
                print(f"   ❌ 帧捕获异常: {e}")
                self.test_stats['api_errors'] += 1
        
        print(f"✅ 完成捕获 {self.test_stats['frames_captured']} 帧")
    
    def create_status_frame(self, status_data, frame_num):
        """创建状态显示帧"""
        # 创建640x480的黑色背景
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # 标题
        cv2.putText(frame, "V8 Stream Test", (200, 40), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 255, 255), 2)
        
        # 帧号和时间
        cv2.putText(frame, f"Frame: {frame_num}", (20, 80), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        cv2.putText(frame, f"Time: {time.strftime('%H:%M:%S')}", (20, 110), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        
        # 系统信息
        sys_info = status_data['system_info']
        y_pos = 150
        cv2.putText(frame, "System Info:", (20, y_pos), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        y_pos += 30
        
        device_text = f"Device: {sys_info['device'].upper()}"
        cv2.putText(frame, device_text, (20, y_pos), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        y_pos += 25
        
        model_status = "Loaded" if sys_info['model_loaded'] else "Not Loaded"
        cv2.putText(frame, f"Model: {model_status}", (20, y_pos), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        y_pos += 25
        
        rtsp_status = "Connected" if sys_info['rtsp_connected'] else "Disconnected"
        cv2.putText(frame, f"RTSP: {rtsp_status}", (20, y_pos), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        
        # 检测信息
        stats = status_data['stats']
        y_pos = 150
        cv2.putText(frame, "Detection Info:", (350, y_pos), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        y_pos += 30
        
        detection_status = "Active" if status_data['detection_enabled'] else "Inactive"
        color = (0, 255, 0) if status_data['detection_enabled'] else (0, 0, 255)
        cv2.putText(frame, f"Status: {detection_status}", (350, y_pos), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 1)
        y_pos += 25
        
        cv2.putText(frame, f"Received: {stats['frames_received']}", (350, y_pos), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        y_pos += 25
        
        cv2.putText(frame, f"Processed: {stats['frames_processed']}", (350, y_pos), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        y_pos += 25
        
        cv2.putText(frame, f"RX FPS: {stats['fps_received']:.1f}", (350, y_pos), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        y_pos += 25
        
        cv2.putText(frame, f"TX FPS: {stats['fps_processed']:.1f}", (350, y_pos), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        
        # 底部信息
        cv2.putText(frame, f"Test by test_rtsp.py", (20, 450), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (128, 128, 128), 1)
        
        return frame
    
    def display_test_window(self):
        """显示测试窗口"""
        print("\n🖥️ 启动显示窗口...")
        print("   按键控制:")
        print("   - S: 启动检测")
        print("   - T: 停止检测")
        print("   - C: 捕获帧")
        print("   - I: 显示信息")
        print("   - Q: 退出")
        
        cv2.namedWindow(self.window_name, cv2.WINDOW_AUTOSIZE)
        self.running = True
        
        while self.running:
            frame_to_show = None
            
            with self.frame_lock:
                if self.current_frame is not None:
                    frame_to_show = self.current_frame.copy()
            
            if frame_to_show is not None:
                cv2.imshow(self.window_name, frame_to_show)
            else:
                # 显示等待画面
                waiting_frame = np.zeros((480, 640, 3), dtype=np.uint8)
                cv2.putText(waiting_frame, "Waiting for V8 data...", (150, 240), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                cv2.imshow(self.window_name, waiting_frame)
            
            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print("🛑 用户退出")
                self.running = False
                break
            elif key == ord('s'):
                self.control_detection('start')
            elif key == ord('t'):
                self.control_detection('stop')
            elif key == ord('c'):
                self.capture_test_frames(3)
            elif key == ord('i'):
                self.test_api_connection()
        
        cv2.destroyAllWindows()
    
    def show_test_summary(self):
        """显示测试总结"""
        elapsed = time.time() - self.test_stats['test_start_time']
        
        print("\n" + "="*60)
        print("📊 V8流画面测试总结")
        print("="*60)
        print(f"测试时间: {elapsed:.1f}秒")
        print(f"API调用: {self.test_stats['api_calls']} 次")
        print(f"API错误: {self.test_stats['api_errors']} 次")
        print(f"捕获帧数: {self.test_stats['frames_captured']} 帧")
        print(f"成功率: {((self.test_stats['api_calls'] / max(self.test_stats['api_calls'] + self.test_stats['api_errors'], 1)) * 100):.1f}%")
        print("="*60)
    
    def run_test(self):
        """运行完整测试"""
        print("\n" + "="*60)
        print("🧪 V8流画面测试器启动")
        print("="*60)
        
        # 1. 测试API连接
        if not self.test_api_connection():
            print("❌ API连接失败，请确保v8.py正在运行")
            return
        
        # 2. 捕获初始帧
        self.capture_test_frames(3)
        
        # 3. 测试检测控制
        print("\n🎮 测试检测控制...")
        self.control_detection('start')
        time.sleep(2)
        self.test_api_connection()
        
        self.control_detection('stop')
        time.sleep(1)
        self.test_api_connection()
        
        # 4. 启动显示窗口
        try:
            self.display_test_window()
        except Exception as e:
            print(f"❌ 显示窗口异常: {e}")
        
        # 5. 显示测试总结
        self.show_test_summary()

def main():
    """主函数"""
    print("🧪 test_rtsp.py - V8流画面测试器")
    print("📺 测试v8.py系统的视频流和API功能")
    print("🎮 提供交互式控制和画面显示")
    
    tester = V8StreamTester()
    tester.run_test()

if __name__ == "__main__":
    main()
