#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTTP视频流查看工具 - 输入主机号即可查看HTTP视频流
"""

import cv2
import argparse
import time
import sys
import numpy as np
import urllib.request

def display_http_stream(host_number, port=5000, path="video_feed"):
    """
    显示HTTP视频流
    
    Args:
        host_number (str): 主机号（IP地址最后一段）
        port (int): HTTP端口号
        path (str): 视频流路径
    """
    # 构建HTTP URL
    http_url = f"http://192.168.0.{host_number}:{port}/{path}"
    print(f"[INFO] 正在连接HTTP流: {http_url}")
    
    # 创建窗口
    window_name = f"HTTP Stream - {http_url}"
    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
    
    # 帧计数和FPS计算
    frame_count = 0
    start_time = time.time()
    fps = 0
    
    try:
        # 使用OpenCV的VideoCapture打开HTTP流
        cap = cv2.VideoCapture(http_url)
        
        if not cap.isOpened():
            print(f"[ERROR] 无法连接到HTTP流: {http_url}")
            return False
        
        print(f"[SUCCESS] 成功连接到HTTP流")
        print(f"[INFO] 按 'q' 键退出，按 's' 键保存截图")
        
        while True:
            # 读取一帧
            ret, frame = cap.read()
            
            # 检查是否成功读取
            if not ret:
                print("[WARNING] 无法读取帧，尝试重新连接...")
                # 重新连接
                cap.release()
                cap = cv2.VideoCapture(http_url)
                if not cap.isOpened():
                    print("[ERROR] 重新连接失败")
                    break
                continue
            
            # 计算FPS
            frame_count += 1
            elapsed_time = time.time() - start_time
            if elapsed_time >= 1.0:
                fps = frame_count / elapsed_time
                frame_count = 0
                start_time = time.time()
            
            # 在帧上显示信息
            # cv2.putText(frame, f"FPS: {fps:.1f}", (10, 30), 
            #             cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            # cv2.putText(frame, f"URL: {http_url}", (10, 70), 
            #             cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # 显示帧
            cv2.imshow(window_name, frame)
            
            # 处理键盘输入
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print("[INFO] 用户退出")
                break
            elif key == ord('s'):
                # 保存截图
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"http_capture_{timestamp}.jpg"
                cv2.imwrite(filename, frame)
                print(f"[INFO] 截图已保存: {filename}")
    
    except KeyboardInterrupt:
        print("[INFO] 用户中断")
    except Exception as e:
        print(f"[ERROR] 发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 释放资源
        if 'cap' in locals() and cap is not None:
            cap.release()
        cv2.destroyAllWindows()
        print("[INFO] 已关闭HTTP流")
    
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="HTTP视频流查看工具")
    parser.add_argument("host_number", nargs='?', help="主机号（IP地址最后一段）")
    parser.add_argument("--port", type=int, default=5000, help="HTTP端口号")
    parser.add_argument("--path", default="video_feed", help="视频流路径")
    
    args = parser.parse_args()
    
    # 如果没有参数，进入交互模式
    if args.host_number is None:
        host_number = input("请输入主机号（IP地址最后一段，如186）: ")
        port = input(f"请输入端口号（默认5000）: ") or "5000"
        path = input("请输入视频流路径（默认video_feed）: ") or "video_feed"
        display_http_stream(host_number, int(port), path)
    else:
        display_http_stream(args.host_number, args.port, args.path)

if __name__ == "__main__":
    main()