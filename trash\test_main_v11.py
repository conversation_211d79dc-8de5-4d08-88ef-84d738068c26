#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_main_v11.py - 测试main_v11.py的新功能
测试内容：
1. 端口自动寻找功能
2. API返回视频流地址功能
3. 端口释放功能
4. 程序稳定性测试
"""

import requests
import time
import socket
import subprocess
import sys
import os

class MainV11Tester:
    def __init__(self):
        self.base_ip = "*************"
        self.test_ports = [5000, 5001, 5002, 5003, 5004]
        self.current_port = None
        self.process = None
        
        print("🧪 main_v11.py 功能测试器")
        print("=" * 50)
    
    def test_port_finding(self):
        """测试端口自动寻找功能"""
        print("\n1️⃣ 测试端口自动寻找功能...")
        
        # 先占用5000端口
        print("   占用端口5000...")
        dummy_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        dummy_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        try:
            dummy_socket.bind(('', 5000))
            dummy_socket.listen(1)
            print("   ✅ 端口5000已被占用")
            
            # 启动main_v11.py
            print("   启动main_v11.py...")
            self.start_main_v11()
            
            # 等待启动
            time.sleep(5)
            
            # 检测实际使用的端口
            for port in self.test_ports[1:]:  # 从5001开始检测
                try:
                    response = requests.get(f"http://{self.base_ip}:{port}", timeout=2)
                    if response.status_code == 200:
                        self.current_port = port
                        print(f"   ✅ main_v11.py自动使用端口: {port}")
                        break
                except:
                    continue
            
            if self.current_port:
                print("   ✅ 端口自动寻找功能正常")
            else:
                print("   ❌ 端口自动寻找功能失败")
                
        finally:
            dummy_socket.close()
        
        return self.current_port is not None
    
    def test_api_video_url(self):
        """测试API返回视频流地址功能"""
        print("\n2️⃣ 测试API返回视频流地址功能...")
        
        if not self.current_port:
            print("   ❌ 无可用端口，跳过测试")
            return False
        
        try:
            # 调用启动检测API
            api_url = f"http://{self.base_ip}:{self.current_port}/api/start"
            print(f"   调用API: {api_url}")
            
            response = requests.post(api_url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"   API响应: {data}")
                
                # 检查是否返回了视频流地址
                if 'video_url' in data:
                    video_url = data['video_url']
                    expected_url = f"http://{self.base_ip}:{self.current_port}/video_feed"
                    
                    if video_url == expected_url:
                        print(f"   ✅ 正确返回视频流地址: {video_url}")
                        
                        # 测试视频流是否可访问
                        try:
                            video_response = requests.get(video_url, timeout=5, stream=True)
                            if video_response.status_code == 200:
                                print("   ✅ 视频流地址可正常访问")
                                return True
                            else:
                                print(f"   ❌ 视频流地址访问失败: {video_response.status_code}")
                        except Exception as e:
                            print(f"   ❌ 视频流地址访问异常: {e}")
                    else:
                        print(f"   ❌ 视频流地址不正确，期望: {expected_url}，实际: {video_url}")
                else:
                    print("   ❌ API响应中没有video_url字段")
            else:
                print(f"   ❌ API调用失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ API测试异常: {e}")
        
        return False
    
    def test_port_cleanup(self):
        """测试端口释放功能"""
        print("\n3️⃣ 测试端口释放功能...")
        
        if not self.current_port:
            print("   ❌ 无可用端口，跳过测试")
            return False
        
        # 记录当前端口状态
        print(f"   当前使用端口: {self.current_port}")
        
        # 停止main_v11.py进程
        print("   停止main_v11.py进程...")
        self.stop_main_v11()
        
        # 等待进程完全退出
        time.sleep(3)
        
        # 测试端口是否被释放
        try:
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            test_socket.bind(('', self.current_port))
            test_socket.close()
            print(f"   ✅ 端口 {self.current_port} 已成功释放")
            return True
        except OSError as e:
            print(f"   ❌ 端口 {self.current_port} 未被释放: {e}")
            return False
    
    def test_stability(self):
        """测试程序稳定性"""
        print("\n4️⃣ 测试程序稳定性...")
        
        # 重新启动程序
        print("   重新启动main_v11.py...")
        self.start_main_v11()
        time.sleep(5)
        
        # 检测新端口
        for port in self.test_ports:
            try:
                response = requests.get(f"http://{self.base_ip}:{port}", timeout=2)
                if response.status_code == 200:
                    self.current_port = port
                    print(f"   ✅ 程序重新启动，使用端口: {port}")
                    break
            except:
                continue
        
        if self.current_port:
            # 进行多次API调用测试
            success_count = 0
            total_tests = 5
            
            for i in range(total_tests):
                try:
                    response = requests.post(f"http://{self.base_ip}:{self.current_port}/api/status", timeout=3)
                    if response.status_code == 200:
                        success_count += 1
                    time.sleep(1)
                except:
                    pass
            
            success_rate = success_count / total_tests * 100
            print(f"   API稳定性测试: {success_count}/{total_tests} ({success_rate:.1f}%)")
            
            if success_rate >= 80:
                print("   ✅ 程序稳定性良好")
                return True
            else:
                print("   ❌ 程序稳定性不足")
        else:
            print("   ❌ 程序重启失败")
        
        return False
    
    def start_main_v11(self):
        """启动main_v11.py进程"""
        try:
            # 使用subprocess启动进程
            self.process = subprocess.Popen(
                [sys.executable, "main_v11.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=".."  # 回到上级目录
            )
            print("   main_v11.py进程已启动")
        except Exception as e:
            print(f"   启动main_v11.py失败: {e}")
    
    def stop_main_v11(self):
        """停止main_v11.py进程"""
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
                print("   main_v11.py进程已停止")
            except subprocess.TimeoutExpired:
                self.process.kill()
                print("   main_v11.py进程已强制终止")
            except Exception as e:
                print(f"   停止main_v11.py失败: {e}")
    
    def run_full_test(self):
        """运行完整测试"""
        print("🧪 main_v11.py 完整功能测试")
        print("=" * 50)
        
        results = []
        
        try:
            # 测试1: 端口自动寻找
            result1 = self.test_port_finding()
            results.append(("端口自动寻找", result1))
            
            # 测试2: API返回视频流地址
            result2 = self.test_api_video_url()
            results.append(("API返回视频流地址", result2))
            
            # 测试3: 端口释放
            result3 = self.test_port_cleanup()
            results.append(("端口释放", result3))
            
            # 测试4: 程序稳定性
            result4 = self.test_stability()
            results.append(("程序稳定性", result4))
            
        finally:
            # 确保清理进程
            self.stop_main_v11()
        
        # 输出测试结果
        print("\n" + "=" * 50)
        print("📊 测试结果汇总")
        print("=" * 50)
        
        passed = 0
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        total = len(results)
        success_rate = passed / total * 100
        print(f"\n总体通过率: {passed}/{total} ({success_rate:.1f}%)")
        
        if success_rate >= 75:
            print("🎉 main_v11.py 功能测试整体通过！")
        else:
            print("⚠️ main_v11.py 存在一些问题，需要进一步检查")
        
        print("=" * 50)

def main():
    """主函数"""
    print("🧪 test_main_v11.py - main_v11.py功能测试")
    print("🎯 测试端口管理、API功能、程序稳定性")
    
    tester = MainV11Tester()
    tester.run_full_test()

if __name__ == "__main__":
    main()
