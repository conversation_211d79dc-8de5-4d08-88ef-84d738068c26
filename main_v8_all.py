#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
main_v8_all.py - RTSP流测试客户端
连接到main_v7_all.py推出的HTTP MJPEG流进行测试和显示
"""

import cv2
import numpy as np
import requests
import time
import threading
from threading import Lock
import os

# 抑制OpenCV日志
os.environ['OPENCV_LOG_LEVEL'] = 'SILENT'
cv2.setLogLevel(0)

class RTSPStreamTester:
    def __init__(self):
        # 配置
        self.stream_url = "http://192.168.0.138:5000/video_feed"
        self.api_base_url = "http://192.168.0.138:5000/api"
        self.window_name = "RTSP Stream Test - main_v8_all"
        
        # 状态变量
        self.running = False
        self.current_frame = None
        self.frame_lock = Lock()
        self.frame_count = 0
        self.fps_counter = 0
        self.fps_timer = time.time()
        
        # 统计信息
        self.stats = {
            'frames_received': 0,
            'connection_errors': 0,
            'last_frame_time': 0,
            'start_time': time.time()
        }
        
        print("🧪 RTSP流测试客户端初始化完成")
        print(f"📺 目标流地址: {self.stream_url}")
        print(f"🔧 API控制地址: {self.api_base_url}")
    
    def test_api_connection(self):
        """测试API连接"""
        try:
            response = requests.post(f"{self.api_base_url}/status", timeout=3)
            if response.status_code == 200:
                data = response.json()
                print("✅ API连接成功")
                print(f"   检测状态: {'启用' if data['detection_enabled'] else '禁用'}")
                print(f"   处理状态: {'激活' if data['processing_active'] else '非激活'}")
                print(f"   接收帧数: {data['stats']['frames_received']}")
                print(f"   处理帧数: {data['stats']['frames_processed']}")
                return True
            else:
                print(f"❌ API连接失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ API连接异常: {e}")
            return False
    
    def control_detection(self, action):
        """控制检测启停"""
        try:
            endpoint = f"{self.api_base_url}/{action}"
            response = requests.post(endpoint, timeout=3)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {action.upper()}命令成功: {data['message']}")
                return True
            else:
                print(f"❌ {action.upper()}命令失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ {action.upper()}命令异常: {e}")
            return False
    
    def stream_reader_thread(self):
        """流读取线程"""
        print("🚀 启动流读取线程...")
        
        while self.running:
            try:
                # 使用requests获取MJPEG流
                response = requests.get(self.stream_url, stream=True, timeout=5)
                
                if response.status_code != 200:
                    print(f"❌ 流连接失败，状态码: {response.status_code}")
                    self.stats['connection_errors'] += 1
                    time.sleep(2)
                    continue
                
                print("✅ 流连接成功，开始接收帧...")
                
                # 解析MJPEG流
                bytes_data = bytes()
                for chunk in response.iter_content(chunk_size=1024):
                    if not self.running:
                        break
                    
                    bytes_data += chunk
                    
                    # 查找JPEG帧边界
                    a = bytes_data.find(b'\xff\xd8')  # JPEG开始
                    b = bytes_data.find(b'\xff\xd9')  # JPEG结束
                    
                    if a != -1 and b != -1:
                        jpg = bytes_data[a:b+2]
                        bytes_data = bytes_data[b+2:]
                        
                        # 解码JPEG
                        frame = cv2.imdecode(np.frombuffer(jpg, dtype=np.uint8), cv2.IMREAD_COLOR)
                        
                        if frame is not None:
                            # 更新统计
                            self.stats['frames_received'] += 1
                            self.stats['last_frame_time'] = time.time()
                            
                            # 添加测试信息到帧上
                            self.add_test_info(frame)
                            
                            # 更新当前帧
                            with self.frame_lock:
                                self.current_frame = frame.copy()
                            
                            # 计算FPS
                            self.fps_counter += 1
                            if time.time() - self.fps_timer >= 1.0:
                                fps = self.fps_counter / (time.time() - self.fps_timer)
                                print(f"📊 接收FPS: {fps:.1f}, 总帧数: {self.stats['frames_received']}")
                                self.fps_counter = 0
                                self.fps_timer = time.time()
                
            except requests.exceptions.Timeout:
                print("⏰ 流连接超时，重试中...")
                self.stats['connection_errors'] += 1
                time.sleep(2)
            except requests.exceptions.ConnectionError:
                print("🔌 流连接断开，重试中...")
                self.stats['connection_errors'] += 1
                time.sleep(2)
            except Exception as e:
                print(f"❌ 流读取异常: {e}")
                self.stats['connection_errors'] += 1
                time.sleep(2)
        
        print("🛑 流读取线程结束")
    
    def add_test_info(self, frame):
        """在帧上添加测试信息"""
        h, w = frame.shape[:2]
        
        # 添加测试标题
        cv2.putText(frame, "RTSP Stream Test - main_v8_all", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 255), 2)
        
        # 添加统计信息
        elapsed = time.time() - self.stats['start_time']
        fps = self.stats['frames_received'] / elapsed if elapsed > 0 else 0
        
        info_text = [
            f"Frames: {self.stats['frames_received']}",
            f"FPS: {fps:.1f}",
            f"Errors: {self.stats['connection_errors']}",
            f"Time: {elapsed:.1f}s"
        ]
        
        for i, text in enumerate(info_text):
            cv2.putText(frame, text, (10, h - 100 + i * 25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
        # 添加控制提示
        controls = [
            "Controls:",
            "S - Start Detection",
            "T - Stop Detection", 
            "Q - Quit",
            "SPACE - Test API"
        ]
        
        for i, text in enumerate(controls):
            cv2.putText(frame, text, (w - 200, 30 + i * 25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    def display_thread(self):
        """显示线程"""
        print("🖥️ 启动显示线程...")
        
        cv2.namedWindow(self.window_name, cv2.WINDOW_AUTOSIZE)
        
        while self.running:
            frame_to_show = None
            
            with self.frame_lock:
                if self.current_frame is not None:
                    frame_to_show = self.current_frame.copy()
            
            if frame_to_show is not None:
                cv2.imshow(self.window_name, frame_to_show)
            else:
                # 显示等待帧
                waiting_frame = np.zeros((480, 640, 3), dtype=np.uint8)
                cv2.putText(waiting_frame, "Waiting for stream...", (200, 240), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                cv2.imshow(self.window_name, waiting_frame)
            
            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print("🛑 用户按下Q键，退出程序")
                self.running = False
                break
            elif key == ord('s'):
                print("🚀 用户按下S键，启动检测")
                self.control_detection('start')
            elif key == ord('t'):
                print("🛑 用户按下T键，停止检测")
                self.control_detection('stop')
            elif key == ord(' '):
                print("🧪 用户按下空格键，测试API")
                self.test_api_connection()
        
        cv2.destroyAllWindows()
        print("🖥️ 显示线程结束")
    
    def run(self):
        """运行测试客户端"""
        print("\n" + "="*60)
        print("🧪 RTSP流测试客户端启动")
        print("="*60)
        
        # 测试API连接
        if not self.test_api_connection():
            print("❌ 无法连接到API，请确保main_v7_all.py正在运行")
            return
        
        self.running = True
        
        # 启动线程
        stream_thread = threading.Thread(target=self.stream_reader_thread, daemon=True)
        display_thread = threading.Thread(target=self.display_thread, daemon=True)
        
        stream_thread.start()
        display_thread.start()
        
        try:
            # 等待显示线程结束
            display_thread.join()
        except KeyboardInterrupt:
            print("\n🛑 收到中断信号，正在退出...")
        
        self.running = False
        
        # 等待流线程结束
        stream_thread.join(timeout=2)
        
        # 打印最终统计
        elapsed = time.time() - self.stats['start_time']
        avg_fps = self.stats['frames_received'] / elapsed if elapsed > 0 else 0
        
        print("\n" + "="*60)
        print("📊 测试统计结果")
        print("="*60)
        print(f"运行时间: {elapsed:.1f}秒")
        print(f"接收帧数: {self.stats['frames_received']}")
        print(f"平均FPS: {avg_fps:.1f}")
        print(f"连接错误: {self.stats['connection_errors']}")
        print("="*60)

def main():
    """主函数"""
    print("🧪 main_v8_all.py - RTSP流测试客户端")
    print("📺 连接到main_v7_all.py的HTTP MJPEG流")
    print("🔧 提供API控制和实时显示功能")
    
    tester = RTSPStreamTester()
    tester.run()

if __name__ == "__main__":
    main()
