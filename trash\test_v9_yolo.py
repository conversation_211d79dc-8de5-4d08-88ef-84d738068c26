#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_v9_yolo.py - 测试v9.py的YOLO检测功能
验证YOLO模型是否正确加载和运行
"""

import requests
import time
import json

class V9YOLOTester:
    def __init__(self):
        self.api_base = "http://192.168.0.142:5001/api"
        self.web_url = "http://192.168.0.142:5001"
        
        print("🧪 V9 YOLO检测测试器")
        print(f"🎯 目标: {self.web_url}")
    
    def test_api_connection(self):
        """测试API连接"""
        try:
            response = requests.post(f"{self.api_base}/status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print("✅ API连接成功")
                return data
            else:
                print(f"❌ API连接失败: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ API连接异常: {e}")
            return None
    
    def start_detection(self):
        """启动检测"""
        try:
            response = requests.post(f"{self.api_base}/start", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 启动检测成功: {data['message']}")
                return True
            else:
                print(f"❌ 启动检测失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 启动检测异常: {e}")
            return False
    
    def stop_detection(self):
        """停止检测"""
        try:
            response = requests.post(f"{self.api_base}/stop", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 停止检测成功: {data['message']}")
                return True
            else:
                print(f"❌ 停止检测失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 停止检测异常: {e}")
            return False
    
    def monitor_detection(self, duration=60):
        """监控检测过程"""
        print(f"\n🔍 监控检测过程 {duration} 秒...")
        
        start_time = time.time()
        last_detections = 0
        
        while time.time() - start_time < duration:
            try:
                response = requests.post(f"{self.api_base}/status", timeout=3)
                if response.status_code == 200:
                    data = response.json()
                    
                    # 显示关键信息
                    stats = data['stats']
                    sys_info = data['system_info']
                    
                    current_detections = stats.get('detections_total', 0)
                    current_frame_detections = stats.get('detections_current', 0)
                    inference_time = stats.get('model_inference_time', 0) * 1000
                    
                    elapsed = time.time() - start_time
                    
                    print(f"[{elapsed:.0f}s] "
                          f"帧: {stats['frames_processed']} | "
                          f"当前检测: {current_frame_detections} | "
                          f"总检测: {current_detections} | "
                          f"推理: {inference_time:.1f}ms | "
                          f"模型: {'✅' if sys_info['model_loaded'] else '❌'}")
                    
                    # 检查检测数是否增长
                    if current_detections > last_detections:
                        print(f"    🎯 检测到新物体！总数从 {last_detections} 增加到 {current_detections}")
                        last_detections = current_detections
                
                time.sleep(3)  # 每3秒检查一次
                
            except Exception as e:
                print(f"❌ 监控异常: {e}")
                time.sleep(5)
        
        print(f"✅ 监控完成，最终检测总数: {last_detections}")
        return last_detections
    
    def run_full_test(self):
        """运行完整测试"""
        print("\n" + "="*60)
        print("🧪 V9 YOLO检测功能完整测试")
        print("="*60)
        
        # 1. 测试连接
        print("\n1️⃣ 测试API连接...")
        initial_status = self.test_api_connection()
        if not initial_status:
            print("❌ 无法连接到v9.py，请确保程序正在运行")
            return
        
        # 显示初始状态
        sys_info = initial_status['system_info']
        print(f"   设备: {sys_info['device'].upper()}")
        print(f"   模型: {'已加载' if sys_info['model_loaded'] else '未加载'}")
        print(f"   RTSP: {'连接' if sys_info['rtsp_connected'] else '断开'}")
        print(f"   支持类别: {len(sys_info.get('yolo_classes', []))} 个")
        
        # 2. 启动检测
        print("\n2️⃣ 启动YOLO检测...")
        if not self.start_detection():
            print("❌ 无法启动检测")
            return
        
        # 等待模型加载
        print("⏳ 等待模型加载...")
        time.sleep(5)
        
        # 3. 监控检测过程
        print("\n3️⃣ 监控检测过程...")
        total_detections = self.monitor_detection(30)  # 监控30秒
        
        # 4. 停止检测
        print("\n4️⃣ 停止检测...")
        self.stop_detection()
        
        # 5. 最终状态
        print("\n5️⃣ 获取最终状态...")
        final_status = self.test_api_connection()
        if final_status:
            stats = final_status['stats']
            print(f"   最终统计:")
            print(f"   - 处理帧数: {stats['frames_processed']}")
            print(f"   - 总检测数: {stats.get('detections_total', 0)}")
            print(f"   - 平均推理时间: {stats.get('model_inference_time', 0)*1000:.1f}ms")
        
        # 6. 测试结论
        print("\n" + "="*60)
        print("📊 测试结论")
        print("="*60)
        
        if total_detections > 0:
            print(f"🎉 YOLO检测功能正常工作！")
            print(f"✅ 成功检测到 {total_detections} 个物体")
            print(f"✅ 模型推理正常")
            print(f"✅ 检测结果统计正确")
        else:
            print(f"⚠️ 未检测到物体")
            print(f"   可能原因:")
            print(f"   - 画面中没有可识别的物体")
            print(f"   - 置信度阈值过高")
            print(f"   - 模型加载失败")
            print(f"   - RTSP流问题")
        
        print("="*60)

def main():
    """主函数"""
    print("🧪 test_v9_yolo.py - V9 YOLO检测功能测试")
    print("🎯 验证v9.py的YOLO检测是否正常工作")
    
    tester = V9YOLOTester()
    tester.run_full_test()

if __name__ == "__main__":
    main()
