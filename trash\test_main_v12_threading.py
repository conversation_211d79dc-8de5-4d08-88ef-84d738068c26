#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_main_v12_threading.py - 测试main_v12.py的多线程功能
专门测试视频流和API请求的并发处理能力
"""

import requests
import time
import threading
import concurrent.futures

class ThreadingTester:
    def __init__(self):
        self.base_ip = "*************"
        self.port = None
        self.api_responses = []
        self.video_responses = []
        
        print("🧪 main_v12.py 多线程功能测试器")
        print("=" * 50)
    
    def find_server_port(self):
        """寻找运行中的main_v12.py服务器端口"""
        print("🔍 寻找main_v12.py服务器...")
        
        for port in range(5000, 5006):
            try:
                response = requests.get(f"http://{self.base_ip}:{port}", timeout=2)
                if response.status_code == 200 and "检测控制台 v12" in response.text:
                    self.port = port
                    print(f"✅ 发现main_v12.py运行在端口: {port}")
                    return True
            except:
                continue
        
        print("❌ 未发现main_v12.py运行实例")
        return False
    
    def test_api_call(self, endpoint, test_id):
        """测试API调用"""
        try:
            start_time = time.time()
            response = requests.post(f"http://{self.base_ip}:{self.port}/api/{endpoint}", timeout=5)
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                result = {
                    'test_id': test_id,
                    'endpoint': endpoint,
                    'success': True,
                    'response_time': end_time - start_time,
                    'thread_id': data.get('thread_id', 'unknown'),
                    'server_type': data.get('server_type', 'unknown'),
                    'timestamp': time.strftime('%H:%M:%S')
                }
                print(f"[API-{test_id}] ✅ {endpoint} 成功 - 响应时间: {result['response_time']:.3f}s - 线程ID: {result['thread_id']}")
            else:
                result = {
                    'test_id': test_id,
                    'endpoint': endpoint,
                    'success': False,
                    'response_time': end_time - start_time,
                    'error': f"HTTP {response.status_code}",
                    'timestamp': time.strftime('%H:%M:%S')
                }
                print(f"[API-{test_id}] ❌ {endpoint} 失败 - HTTP {response.status_code}")
            
            self.api_responses.append(result)
            return result
            
        except Exception as e:
            result = {
                'test_id': test_id,
                'endpoint': endpoint,
                'success': False,
                'error': str(e),
                'timestamp': time.strftime('%H:%M:%S')
            }
            print(f"[API-{test_id}] ❌ {endpoint} 异常 - {e}")
            self.api_responses.append(result)
            return result
    
    def test_video_stream(self, test_id, duration=5):
        """测试视频流访问"""
        try:
            start_time = time.time()
            response = requests.get(f"http://{self.base_ip}:{self.port}/video_feed", 
                                  timeout=duration+1, stream=True)
            
            if response.status_code == 200:
                bytes_received = 0
                frames_received = 0
                
                # 读取视频流数据
                for chunk in response.iter_content(chunk_size=1024):
                    if chunk:
                        bytes_received += len(chunk)
                        if b'--frame' in chunk:
                            frames_received += 1
                    
                    # 检查是否超时
                    if time.time() - start_time > duration:
                        break
                
                end_time = time.time()
                result = {
                    'test_id': test_id,
                    'success': True,
                    'duration': end_time - start_time,
                    'bytes_received': bytes_received,
                    'frames_received': frames_received,
                    'timestamp': time.strftime('%H:%M:%S')
                }
                print(f"[VIDEO-{test_id}] ✅ 视频流成功 - 时长: {result['duration']:.1f}s - 帧数: {frames_received} - 数据: {bytes_received}字节")
            else:
                result = {
                    'test_id': test_id,
                    'success': False,
                    'error': f"HTTP {response.status_code}",
                    'timestamp': time.strftime('%H:%M:%S')
                }
                print(f"[VIDEO-{test_id}] ❌ 视频流失败 - HTTP {response.status_code}")
            
            self.video_responses.append(result)
            return result
            
        except Exception as e:
            result = {
                'test_id': test_id,
                'success': False,
                'error': str(e),
                'timestamp': time.strftime('%H:%M:%S')
            }
            print(f"[VIDEO-{test_id}] ❌ 视频流异常 - {e}")
            self.video_responses.append(result)
            return result
    
    def test_concurrent_access(self):
        """测试并发访问 - 关键测试"""
        print("\n🎯 测试并发访问能力...")
        print("   同时进行视频流访问和API调用，验证是否互相阻塞")
        
        # 清空之前的结果
        self.api_responses.clear()
        self.video_responses.clear()
        
        # 使用线程池进行并发测试
        with concurrent.futures.ThreadPoolExecutor(max_workers=6) as executor:
            futures = []
            
            # 启动视频流测试（持续10秒）
            futures.append(executor.submit(self.test_video_stream, 1, 10))
            futures.append(executor.submit(self.test_video_stream, 2, 10))
            
            # 等待1秒，确保视频流开始
            time.sleep(1)
            
            # 在视频流运行期间进行API调用
            for i in range(4):
                futures.append(executor.submit(self.test_api_call, 'status', i+1))
                time.sleep(0.5)  # 每0.5秒一次API调用
            
            # 等待所有任务完成
            concurrent.futures.wait(futures, timeout=15)
        
        # 分析结果
        api_success = sum(1 for r in self.api_responses if r['success'])
        video_success = sum(1 for r in self.video_responses if r['success'])
        
        print(f"\n📊 并发测试结果:")
        print(f"   API调用: {api_success}/{len(self.api_responses)} 成功")
        print(f"   视频流: {video_success}/{len(self.video_responses)} 成功")
        
        # 检查响应时间
        api_times = [r['response_time'] for r in self.api_responses if r['success'] and 'response_time' in r]
        if api_times:
            avg_api_time = sum(api_times) / len(api_times)
            max_api_time = max(api_times)
            print(f"   API响应时间: 平均 {avg_api_time:.3f}s, 最大 {max_api_time:.3f}s")
            
            # 判断是否被阻塞（响应时间过长表示可能被阻塞）
            if max_api_time > 2.0:
                print("   ⚠️ API响应时间较长，可能存在阻塞")
            else:
                print("   ✅ API响应时间正常，无明显阻塞")
        
        return api_success == len(self.api_responses) and video_success == len(self.video_responses)
    
    def test_thread_isolation(self):
        """测试线程隔离 - 验证不同请求使用不同线程"""
        print("\n🧵 测试线程隔离...")
        
        # 进行多次API调用，检查线程ID
        thread_ids = set()
        for i in range(5):
            result = self.test_api_call('status', f'thread-{i+1}')
            if result['success'] and 'thread_id' in result:
                thread_ids.add(result['thread_id'])
            time.sleep(0.2)
        
        print(f"   发现 {len(thread_ids)} 个不同的处理线程")
        if len(thread_ids) > 1:
            print("   ✅ 多线程服务器正常工作，使用不同线程处理请求")
            return True
        else:
            print("   ⚠️ 所有请求使用相同线程，可能不是多线程服务器")
            return False
    
    def run_full_test(self):
        """运行完整的多线程测试"""
        print("🧪 main_v12.py 多线程功能完整测试")
        print("=" * 50)
        
        # 1. 寻找服务器
        if not self.find_server_port():
            print("❌ 无法找到运行中的main_v12.py实例")
            print("💡 请先启动main_v12.py，然后再运行此测试")
            return
        
        results = []
        
        # 2. 测试并发访问
        print(f"\n🎯 使用端口 {self.port} 进行多线程测试...")
        concurrent_result = self.test_concurrent_access()
        results.append(("并发访问测试", concurrent_result))
        
        # 3. 测试线程隔离
        thread_result = self.test_thread_isolation()
        results.append(("线程隔离测试", thread_result))
        
        # 4. 输出测试结果
        print("\n" + "=" * 50)
        print("📊 多线程测试结果汇总")
        print("=" * 50)
        
        passed = 0
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        total = len(results)
        success_rate = passed / total * 100
        
        print(f"\n总体通过率: {passed}/{total} ({success_rate:.1f}%)")
        
        if success_rate >= 100:
            print("🎉 main_v12.py 多线程功能完全正常！")
            print("✅ 视频流和API请求可以并发处理，无阻塞问题")
        elif success_rate >= 50:
            print("⚠️ main_v12.py 多线程功能部分正常")
            print("💡 建议检查服务器配置")
        else:
            print("❌ main_v12.py 多线程功能存在问题")
            print("💡 可能仍在使用单线程服务器")
        
        print("=" * 50)

def main():
    """主函数"""
    print("🧪 test_main_v12_threading.py - 多线程功能测试")
    print("🎯 验证视频流和API请求的并发处理能力")
    
    tester = ThreadingTester()
    tester.run_full_test()

if __name__ == "__main__":
    main()
