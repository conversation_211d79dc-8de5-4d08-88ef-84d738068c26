#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_system_info.py - 测试system_info结构
"""

# 模拟main_v12.py中的system_info结构
system_info = {
    'device': 'cpu',
    'model_loaded': False,
    'model_path': 'yolov8n.pt',
    'rtsp_url': 'rtsp://*************:8554/camera',
    'rtsp_connected': False,
    'rtsp_latency': 0,  # RTSP通信延迟时间(毫秒)
    'python_version': '',
    'opencv_version': '',
    'torch_version': '',
    'local_ip': '',
    'yolo_classes': [],
    'http_port': 0,  # 实际使用的HTTP端口
}

print("测试system_info结构:")
print("字段列表:", sorted(system_info.keys()))
print("rtsp_latency字段存在:", 'rtsp_latency' in system_info)
print("server_type字段存在:", 'server_type' in system_info)
print("rtsp_latency值:", system_info['rtsp_latency'])

# 模拟设置延迟
system_info['rtsp_latency'] = 15.3
print("设置延迟后:", system_info['rtsp_latency'])
