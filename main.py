import cv2
import torch
import numpy as np
from ultralytics import YOLO
import requests
from threading import Thread
from queue import Queue, Empty
from time import time, sleep
import sys

# 打印环境信息
print("=" * 50)
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"OpenCV version: {cv2.__version__}")
print("=" * 50)

# 初始化YOLOv11模型
MODEL_PATH = 'yolov8n.pt'
print(f"[DEBUG] 正在加载模型: {MODEL_PATH}")
try:
    model = YOLO(MODEL_PATH)
    print("[DEBUG] 模型加载成功！")
except Exception as e:
    print(f"[ERROR] 模型加载失败: {e}")
    sys.exit(1)

# 定义要检测的垃圾类别ID（COCO数据集类别映射）
GARBAGE_CLASSES = {
    39: 'bottle',  # 瓶子
    67: 'cell phone'  # 纸巾等（使用相近类别）
}
print(f"[DEBUG] 检测类别配置: {GARBAGE_CLASSES}")

# 性能优化参数
FRAME_BUFFER_SIZE = 3  # 帧缓冲队列大小
CONFIDENCE_THRESHOLD = 0.5  # 置信度阈值
print(f"[CONFIG] 队列大小: {FRAME_BUFFER_SIZE}, 置信度阈值: {CONFIDENCE_THRESHOLD}")

# 多线程处理队列
frame_queue = Queue(maxsize=FRAME_BUFFER_SIZE)
results_queue = Queue(maxsize=FRAME_BUFFER_SIZE)

# 调试统计
stats = {
    'frames_received': 0,
    'frames_processed': 0,
    'frames_displayed': 0,
    'queue_drops': 0,
    'start_time': time()
}


def process_frames():
    """多线程处理帧"""
    print("[THREAD] 帧处理线程启动")
    while True:
        try:
            # 获取帧，设置超时避免永久阻塞
            frame = frame_queue.get(timeout=5.0)
            if frame is None:  # 终止信号
                print("[THREAD] 收到终止信号，退出处理线程")
                break

            # 记录处理开始时间
            process_start = time()

            # 使用YOLOv11进行检测
            results = model(frame, imgsz=640, conf=CONFIDENCE_THRESHOLD, verbose=False)[0]

            # 提取检测结果
            detections = []
            for box in results.boxes:
                class_id = int(box.cls)
                if class_id in GARBAGE_CLASSES:
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    conf = float(box.conf)
                    label = f"{GARBAGE_CLASSES[class_id]} {conf:.2f}"
                    detections.append((x1, y1, x2, y2, label))

            # 统计处理帧数
            stats['frames_processed'] += 1

            # 放入结果队列
            results_queue.put((frame, detections))

            # 打印处理耗时
            process_time = time() - process_start
            print(f"[PROCESS] 帧处理耗时: {process_time:.3f}s, 检测到目标: {len(detections)}个")

        except Empty:
            print("[WARNING] 帧处理线程等待超时，继续等待...")
            continue
        except Exception as e:
            print(f"[ERROR] 帧处理异常: {e}")
            continue


def draw_detections(frame, detections):
    """在帧上绘制检测框"""
    for (x1, y1, x2, y2, label) in detections:
        # 绘制矩形框
        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        # 添加标签
        cv2.putText(frame, label, (x1, y1 - 10),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
    return frame


def video_stream_reader(url, max_retries=50, retry_interval=5):
    """从HTTP视频流读取帧（改进版 - 模拟浏览器行为）"""
    retries = 0
    session = requests.Session()

    # 设置浏览器级别的请求头，模拟Edge浏览器
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
    }

    while retries < max_retries:
        print(f"[STREAM] 正在连接视频流 ({retries + 1}/{max_retries}): {url}")
        try:
            # 使用更长的超时时间和浏览器头
            response = session.get(
                url,
                stream=True,
                timeout=(30, 60),  # (连接超时30秒, 读取超时60秒)
                headers=headers,
                allow_redirects=True
            )

            print(f"[DEBUG] HTTP状态码: {response.status_code}")
            print(f"[DEBUG] Content-Type: {response.headers.get('content-type', 'Unknown')}")
            print(f"[DEBUG] Content-Length: {response.headers.get('content-length', 'Unknown')}")

            if response.status_code != 200:
                print(f"[ERROR] 视频流连接失败，状态码: {response.status_code}")
                retries += 1
                sleep(retry_interval)
                continue

            print("[STREAM] 视频流连接成功")
            bytes_data = b''
            frame_count = 0
            empty_count = 0
            max_empty = 20  # 允许更多空读取

            # 使用iter_content而不是raw.read，更稳定
            for chunk in response.iter_content(chunk_size=4096):
                if not chunk:
                    empty_count += 1
                    if empty_count > max_empty:
                        print(f"[WARNING] 连续{max_empty}次空读取，可能连接中断")
                        break
                    sleep(0.01)  # 短暂等待
                    continue

                empty_count = 0  # 重置空读取计数
                bytes_data += chunk

                # 处理JPEG帧
                while True:
                    start_pos = bytes_data.find(b'\xff\xd8')  # JPEG起始标记
                    end_pos = bytes_data.find(b'\xff\xd9')    # JPEG结束标记

                    if start_pos == -1 or end_pos == -1 or end_pos <= start_pos:
                        break

                    # 提取完整JPEG帧
                    jpeg_frame = bytes_data[start_pos:end_pos + 2]
                    bytes_data = bytes_data[end_pos + 2:]

                    try:
                        # 解码帧
                        frame = cv2.imdecode(np.frombuffer(jpeg_frame, dtype=np.uint8), cv2.IMREAD_COLOR)
                        if frame is not None:
                            stats['frames_received'] += 1
                            frame_count += 1
                            yield frame

                            # 每100帧打印一次进度
                            if frame_count % 100 == 0:
                                print(f"[STREAM] 已接收 {frame_count} 帧")
                        else:
                            print("[WARNING] 帧解码结果为空")
                    except Exception as e:
                        print(f"[ERROR] 帧解码错误: {e}")

                    # 继续寻找下一帧
                    start_pos = bytes_data.find(b'\xff\xd8')
                    end_pos = bytes_data.find(b'\xff\xd9')

            # 如果成功接收到帧，说明连接是好的，重置重试计数
            if frame_count > 0:
                print(f"[STREAM] 本次连接接收了 {frame_count} 帧，连接中断，准备重连...")
                retries = max(0, retries - 1)  # 成功后减少重试计数
            else:
                print("[STREAM] 本次连接未接收到任何帧")
                retries += 1

            # 动态调整重连间隔
            current_interval = retry_interval + (retries * 2)  # 递增间隔
            print(f"[STREAM] 等待 {current_interval} 秒后重新连接...")
            sleep(current_interval)

        except requests.exceptions.Timeout as e:
            print(f"[ERROR] 连接超时: {e}")
            retries += 1
            sleep(retry_interval)
        except requests.exceptions.ConnectionError as e:
            print(f"[ERROR] 连接错误: {e}")
            retries += 1
            sleep(retry_interval)
        except requests.exceptions.RequestException as e:
            print(f"[ERROR] 请求异常: {e}")
            retries += 1
            sleep(retry_interval)
        except Exception as e:
            print(f"[ERROR] 未知异常: {e}")
            retries += 1
            sleep(retry_interval)

    print(f"[ERROR] 超过最大重试次数({max_retries})，视频流连接终止")
    session.close()


def print_stats():
    """打印运行统计信息"""
    elapsed = time() - stats['start_time']
    fps_received = stats['frames_received'] / elapsed if elapsed > 0 else 0
    fps_processed = stats['frames_processed'] / elapsed if elapsed > 0 else 0
    fps_displayed = stats['frames_displayed'] / elapsed if elapsed > 0 else 0

    print("\n" + "=" * 50)
    print(f"运行时间: {elapsed:.1f}秒")
    print(f"收到帧数: {stats['frames_received']} ({fps_received:.1f} FPS)")
    print(f"处理帧数: {stats['frames_processed']} ({fps_processed:.1f} FPS)")
    print(f"显示帧数: {stats['frames_displayed']} ({fps_displayed:.1f} FPS)")
    print(f"队列丢弃帧数: {stats['queue_drops']}")
    print("=" * 50 + "\n")


# 启动处理线程
print("[MAIN] 启动处理线程")
processing_thread = Thread(target=process_frames)
processing_thread.daemon = True
processing_thread.start()

# 主处理循环
# stream_url = "http://192.168.0.125:4747/video"
stream_url = "http://192.168.0.186:8889/camera/"  # 用户提供的可访问视频流
print(f"[MAIN] 启动主循环，视频源: {stream_url}")
fps_counter = 0
fps_timer = time()
last_stat_time = time()

try:
    for frame in video_stream_reader(stream_url):
        if frame is None:
            print("[WARNING] 收到空帧，跳过")
            continue

        # 尝试放入处理队列（非阻塞方式）
        try:
            # 如果队列满，尝试移除旧帧
            if frame_queue.full():
                try:
                    frame_queue.get_nowait()  # 丢弃一帧
                    stats['queue_drops'] += 1
                    print("[QUEUE] 队列已满，丢弃一帧")
                except Empty:
                    pass

            # 放入当前帧
            frame_queue.put_nowait(frame.copy())
            print(f"[QUEUE] 成功放入帧 (队列大小: {frame_queue.qsize()}/{FRAME_BUFFER_SIZE})")
        except Exception as e:
            print(f"[ERROR] 放入队列失败: {e}")

        # 尝试从结果队列获取处理好的帧（非阻塞）
        try:
            processed_frame, detections = results_queue.get_nowait()
            frame_with_boxes = draw_detections(processed_frame, detections)

            # 计算并显示FPS
            fps_counter += 1
            current_time = time()
            if current_time - fps_timer >= 1.0:
                fps = fps_counter / (current_time - fps_timer)
                cv2.putText(frame_with_boxes, f"FPS: {fps:.1f}", (10, 30),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                fps_counter = 0
                fps_timer = current_time

            # 更新显示帧数统计
            stats['frames_displayed'] += 1

            # 显示实时结果
            cv2.imshow('Garbage Detection (YOLOv11)', frame_with_boxes)
        except Empty:
            pass  # 结果队列为空是正常现象，无需处理
        except Exception as e:
            print(f"[ERROR] 结果处理异常: {e}")

        # 按'q'退出
        if cv2.waitKey(1) & 0xFF == ord('q'):
            print("[MAIN] 用户请求退出")
            break

        # 每隔10秒打印一次统计信息
        if time() - last_stat_time > 10:
            print_stats()
            last_stat_time = time()

except KeyboardInterrupt:
    print("[MAIN] 程序被用户中断")
except Exception as e:
    print(f"[ERROR] 主循环异常: {e}")
    import traceback

    traceback.print_exc()
finally:
    print("[MAIN] 清理资源...")
    # 发送终止信号
    frame_queue.put(None)
    # 等待线程结束
    processing_thread.join(timeout=5.0)
    if processing_thread.is_alive():
        print("[WARNING] 处理线程未正常退出")
    else:
        print("[INFO] 处理线程已退出")

    # 打印最终统计
    print_stats()

    # 关闭窗口
    cv2.destroyAllWindows()
    print("[MAIN] 程序退出")