#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_rtsp_simple.py - 简单的画面查看器
直接显示captured_frames目录中的画面，验证v8.py的画面捕获功能
"""

import cv2
import os
import time
import glob

# 抑制OpenCV日志
os.environ['OPENCV_LOG_LEVEL'] = 'SILENT'
cv2.setLogLevel(0)

class SimpleFrameViewer:
    def __init__(self):
        self.frames_dir = "captured_frames"
        self.window_name = "V8 Captured Frames Viewer"
        self.current_index = 0
        self.frame_files = []
        self.playing = False
        self.fps = 10  # 播放速度
        
        print("🎬 简单画面查看器初始化")
        print(f"📁 画面目录: {self.frames_dir}")
        
        # 加载所有帧文件
        self.load_frame_files()
    
    def load_frame_files(self):
        """加载所有帧文件"""
        if not os.path.exists(self.frames_dir):
            print(f"❌ 目录不存在: {self.frames_dir}")
            return False
        
        # 获取所有jpg文件并排序
        pattern = os.path.join(self.frames_dir, "frame_*.jpg")
        self.frame_files = sorted(glob.glob(pattern))
        
        if not self.frame_files:
            print(f"❌ 在 {self.frames_dir} 中没有找到帧文件")
            return False
        
        print(f"✅ 找到 {len(self.frame_files)} 个帧文件")
        print(f"   第一帧: {os.path.basename(self.frame_files[0])}")
        print(f"   最后帧: {os.path.basename(self.frame_files[-1])}")
        return True
    
    def get_current_frame(self):
        """获取当前帧"""
        if not self.frame_files or self.current_index >= len(self.frame_files):
            return None
        
        frame_path = self.frame_files[self.current_index]
        frame = cv2.imread(frame_path)
        
        if frame is not None:
            # 添加信息到帧上
            h, w = frame.shape[:2]
            
            # 添加播放信息
            info_text = f"Frame {self.current_index + 1}/{len(self.frame_files)}"
            cv2.putText(frame, info_text, (400, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 255), 2)
            
            filename = os.path.basename(frame_path)
            cv2.putText(frame, filename, (400, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            status = "Playing" if self.playing else "Paused"
            cv2.putText(frame, f"Status: {status} (FPS: {self.fps})", (400, 90), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            
            # 添加控制说明
            cv2.putText(frame, "Controls: SPACE=Play/Pause, A/D=Prev/Next, +/-=Speed, Q=Quit", 
                       (10, h-20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (128, 128, 128), 1)
        
        return frame
    
    def next_frame(self):
        """下一帧"""
        if self.frame_files and self.current_index < len(self.frame_files) - 1:
            self.current_index += 1
            return True
        return False
    
    def prev_frame(self):
        """上一帧"""
        if self.frame_files and self.current_index > 0:
            self.current_index -= 1
            return True
        return False
    
    def jump_to_frame(self, index):
        """跳转到指定帧"""
        if 0 <= index < len(self.frame_files):
            self.current_index = index
            return True
        return False
    
    def show_frame_info(self):
        """显示当前帧信息"""
        if not self.frame_files:
            print("❌ 没有可用的帧")
            return
        
        frame_path = self.frame_files[self.current_index]
        filename = os.path.basename(frame_path)
        file_size = os.path.getsize(frame_path)
        
        # 读取帧获取尺寸
        frame = cv2.imread(frame_path)
        if frame is not None:
            h, w, c = frame.shape
            print(f"\n📊 当前帧信息:")
            print(f"   文件: {filename}")
            print(f"   索引: {self.current_index + 1}/{len(self.frame_files)}")
            print(f"   尺寸: {w}x{h}x{c}")
            print(f"   大小: {file_size:,} 字节")
            print(f"   路径: {frame_path}")
        else:
            print(f"❌ 无法读取帧: {filename}")
    
    def run_viewer(self):
        """运行查看器"""
        if not self.frame_files:
            print("❌ 没有可用的帧文件")
            return
        
        print("\n🎬 启动画面查看器")
        print("📋 控制说明:")
        print("   SPACE - 播放/暂停")
        print("   A/D - 上一帧/下一帧")
        print("   +/- - 增加/减少播放速度")
        print("   HOME/END - 跳到开始/结束")
        print("   I - 显示帧信息")
        print("   R - 重新加载帧文件")
        print("   Q - 退出")
        
        cv2.namedWindow(self.window_name, cv2.WINDOW_AUTOSIZE)
        
        last_time = time.time()
        
        while True:
            current_time = time.time()
            
            # 自动播放
            if self.playing and (current_time - last_time) >= (1.0 / self.fps):
                if not self.next_frame():
                    # 播放到最后一帧，停止播放
                    self.playing = False
                    print("📺 播放完成")
                last_time = current_time
            
            # 显示当前帧
            frame = self.get_current_frame()
            if frame is not None:
                cv2.imshow(self.window_name, frame)
            else:
                # 显示错误信息
                error_frame = cv2.imread("captured_frames/frame_000001.jpg")
                if error_frame is not None:
                    cv2.putText(error_frame, "Frame Load Error", (200, 240), 
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                    cv2.imshow(self.window_name, error_frame)
            
            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord('q'):
                print("🛑 用户退出")
                break
            elif key == ord(' '):  # 空格键
                self.playing = not self.playing
                status = "播放" if self.playing else "暂停"
                print(f"🎮 {status}")
            elif key == ord('a'):  # 上一帧
                if self.prev_frame():
                    print(f"⬅️ 帧 {self.current_index + 1}")
                self.playing = False
            elif key == ord('d'):  # 下一帧
                if self.next_frame():
                    print(f"➡️ 帧 {self.current_index + 1}")
                self.playing = False
            elif key == ord('+') or key == ord('='):  # 增加速度
                self.fps = min(self.fps + 2, 30)
                print(f"⚡ 播放速度: {self.fps} FPS")
            elif key == ord('-'):  # 减少速度
                self.fps = max(self.fps - 2, 1)
                print(f"🐌 播放速度: {self.fps} FPS")
            elif key == 0:  # HOME键
                self.jump_to_frame(0)
                print("🏠 跳到开始")
                self.playing = False
            elif key == 1:  # END键
                self.jump_to_frame(len(self.frame_files) - 1)
                print("🔚 跳到结束")
                self.playing = False
            elif key == ord('i'):  # 显示信息
                self.show_frame_info()
            elif key == ord('r'):  # 重新加载
                print("🔄 重新加载帧文件...")
                old_count = len(self.frame_files)
                self.load_frame_files()
                new_count = len(self.frame_files)
                print(f"📊 帧数变化: {old_count} → {new_count}")
        
        cv2.destroyAllWindows()
        print("🎉 查看器结束")

def main():
    """主函数"""
    print("🎬 test_rtsp_simple.py - 简单画面查看器")
    print("📺 查看v8.py捕获的画面帧")
    print("🎮 提供播放控制功能")
    
    viewer = SimpleFrameViewer()
    viewer.run_viewer()

if __name__ == "__main__":
    main()
