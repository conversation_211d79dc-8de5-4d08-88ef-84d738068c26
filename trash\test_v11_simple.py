#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_v11_simple.py - 简单测试main_v11.py的新功能
快速验证：
1. 端口自动寻找
2. API返回视频流地址
3. 程序基本功能
"""

import requests
import time
import socket

def test_port_auto_find():
    """测试端口自动寻找功能"""
    print("🔍 测试端口自动寻找功能...")
    
    # 检测5000-5005端口范围内哪个被main_v11.py使用
    base_ip = "*************"
    found_port = None
    
    for port in range(5000, 5006):
        try:
            response = requests.get(f"http://{base_ip}:{port}", timeout=2)
            if response.status_code == 200 and "检测控制台 v11" in response.text:
                found_port = port
                print(f"✅ 发现main_v11.py运行在端口: {port}")
                break
        except:
            continue
    
    if found_port:
        return found_port
    else:
        print("❌ 未发现main_v11.py运行实例")
        return None

def test_api_video_url(port):
    """测试API返回视频流地址功能"""
    print(f"📺 测试API返回视频流地址功能 (端口{port})...")
    
    base_ip = "*************"
    api_url = f"http://{base_ip}:{port}/api/start"
    
    try:
        response = requests.post(api_url, timeout=5)
        if response.status_code == 200:
            data = response.json()
            
            if 'video_url' in data:
                video_url = data['video_url']
                expected_url = f"http://{base_ip}:{port}/video_feed"
                
                if video_url == expected_url:
                    print(f"✅ API正确返回视频流地址: {video_url}")
                    
                    # 测试视频流是否可访问
                    try:
                        video_response = requests.get(video_url, timeout=3, stream=True)
                        if video_response.status_code == 200:
                            print("✅ 视频流地址可正常访问")
                            return True
                        else:
                            print(f"❌ 视频流访问失败: {video_response.status_code}")
                    except Exception as e:
                        print(f"❌ 视频流访问异常: {e}")
                else:
                    print(f"❌ 视频流地址错误，期望: {expected_url}，实际: {video_url}")
            else:
                print("❌ API响应中缺少video_url字段")
                print(f"   实际响应: {data}")
        else:
            print(f"❌ API调用失败: {response.status_code}")
    except Exception as e:
        print(f"❌ API测试异常: {e}")
    
    return False

def test_basic_functions(port):
    """测试基本功能"""
    print(f"⚙️ 测试基本功能 (端口{port})...")
    
    base_ip = "*************"
    
    # 测试状态API
    try:
        status_response = requests.post(f"http://{base_ip}:{port}/api/status", timeout=3)
        if status_response.status_code == 200:
            status_data = status_response.json()
            print("✅ 状态API正常")
            
            # 检查系统信息
            if 'system_info' in status_data:
                sys_info = status_data['system_info']
                print(f"   设备: {sys_info.get('device', 'unknown')}")
                print(f"   HTTP端口: {sys_info.get('http_port', 'unknown')}")
                print(f"   模型状态: {'已加载' if sys_info.get('model_loaded', False) else '未加载'}")
            
            return True
        else:
            print(f"❌ 状态API失败: {status_response.status_code}")
    except Exception as e:
        print(f"❌ 状态API异常: {e}")
    
    return False

def main():
    """主函数"""
    print("🧪 main_v11.py 简单功能测试")
    print("=" * 40)
    print("📋 测试项目:")
    print("  1. 端口自动寻找")
    print("  2. API返回视频流地址")
    print("  3. 基本功能验证")
    print("=" * 40)
    
    # 测试1: 端口自动寻找
    port = test_port_auto_find()
    if not port:
        print("\n❌ 无法找到运行中的main_v11.py实例")
        print("💡 请先启动main_v11.py，然后再运行此测试")
        return
    
    print(f"\n🎯 使用端口 {port} 进行后续测试...")
    
    # 测试2: API返回视频流地址
    video_test_result = test_api_video_url(port)
    
    # 测试3: 基本功能
    basic_test_result = test_basic_functions(port)
    
    # 汇总结果
    print("\n" + "=" * 40)
    print("📊 测试结果汇总")
    print("=" * 40)
    print(f"端口自动寻找: ✅ 通过 (端口{port})")
    print(f"API返回视频流地址: {'✅ 通过' if video_test_result else '❌ 失败'}")
    print(f"基本功能验证: {'✅ 通过' if basic_test_result else '❌ 失败'}")
    
    total_passed = 1 + (1 if video_test_result else 0) + (1 if basic_test_result else 0)
    success_rate = total_passed / 3 * 100
    
    print(f"\n总体通过率: {total_passed}/3 ({success_rate:.1f}%)")
    
    if success_rate >= 66:
        print("🎉 main_v11.py 主要功能正常！")
    else:
        print("⚠️ main_v11.py 存在问题，需要检查")
    
    print("=" * 40)

if __name__ == "__main__":
    main()
