#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的流测试脚本
测试main_v7_all.py的HTTP MJPEG流连接
"""

import requests
import cv2
import numpy as np
import time

def test_stream_connection():
    """测试流连接"""
    stream_url = "http://192.168.0.138:5000/video_feed"
    api_url = "http://192.168.0.138:5000/api/status"
    
    print("🧪 简单流连接测试")
    print("=" * 40)
    
    # 1. 测试API连接
    print("1️⃣ 测试API连接...")
    try:
        response = requests.post(api_url, timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ API连接成功")
            print(f"   检测状态: {'启用' if data['detection_enabled'] else '禁用'}")
            print(f"   接收帧数: {data['stats']['frames_received']}")
            print(f"   处理帧数: {data['stats']['frames_processed']}")
        else:
            print(f"❌ API连接失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API连接异常: {e}")
        return False
    
    # 2. 测试流连接
    print("\n2️⃣ 测试流连接...")
    try:
        response = requests.get(stream_url, stream=True, timeout=5)
        if response.status_code == 200:
            print("✅ 流连接成功")
            print(f"   Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
        else:
            print(f"❌ 流连接失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 流连接异常: {e}")
        return False
    
    # 3. 尝试接收几帧
    print("\n3️⃣ 尝试接收帧...")
    try:
        frame_count = 0
        bytes_data = bytes()
        start_time = time.time()
        
        for chunk in response.iter_content(chunk_size=1024):
            bytes_data += chunk
            
            # 查找JPEG帧边界
            a = bytes_data.find(b'\xff\xd8')  # JPEG开始
            b = bytes_data.find(b'\xff\xd9')  # JPEG结束
            
            if a != -1 and b != -1:
                jpg = bytes_data[a:b+2]
                bytes_data = bytes_data[b+2:]
                
                # 解码JPEG
                frame = cv2.imdecode(np.frombuffer(jpg, dtype=np.uint8), cv2.IMREAD_COLOR)
                
                if frame is not None:
                    frame_count += 1
                    h, w = frame.shape[:2]
                    print(f"   帧 {frame_count}: {w}x{h}")
                    
                    # 保存前几帧作为测试
                    if frame_count <= 3:
                        filename = f"test_frame_{frame_count}.jpg"
                        cv2.imwrite(filename, frame)
                        print(f"   保存帧: {filename}")
                    
                    # 接收5帧后停止
                    if frame_count >= 5:
                        break
            
            # 超时保护
            if time.time() - start_time > 10:
                print("   ⏰ 接收超时")
                break
        
        elapsed = time.time() - start_time
        fps = frame_count / elapsed if elapsed > 0 else 0
        print(f"\n📊 接收统计:")
        print(f"   接收帧数: {frame_count}")
        print(f"   耗时: {elapsed:.1f}秒")
        print(f"   平均FPS: {fps:.1f}")
        
        return frame_count > 0
        
    except Exception as e:
        print(f"❌ 帧接收异常: {e}")
        return False

def test_api_control():
    """测试API控制"""
    api_base = "http://192.168.0.138:5000/api"
    
    print("\n4️⃣ 测试API控制...")
    
    try:
        # 启动检测
        print("   启动检测...")
        response = requests.post(f"{api_base}/start", timeout=3)
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 启动成功: {result['message']}")
        else:
            print(f"   ❌ 启动失败: {response.status_code}")
        
        # 等待2秒
        time.sleep(2)
        
        # 检查状态
        response = requests.post(f"{api_base}/status", timeout=3)
        if response.status_code == 200:
            data = response.json()
            print(f"   检测状态: {'启用' if data['detection_enabled'] else '禁用'}")
            print(f"   处理帧数: {data['stats']['frames_processed']}")
        
        # 停止检测
        print("   停止检测...")
        response = requests.post(f"{api_base}/stop", timeout=3)
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 停止成功: {result['message']}")
        else:
            print(f"   ❌ 停止失败: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ API控制异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 main_v7_all.py 流测试工具")
    print("📺 测试HTTP MJPEG流连接和API控制")
    
    # 测试流连接
    if test_stream_connection():
        print("\n✅ 流连接测试通过")
    else:
        print("\n❌ 流连接测试失败")
        return
    
    # 测试API控制
    if test_api_control():
        print("\n✅ API控制测试通过")
    else:
        print("\n❌ API控制测试失败")
    
    print("\n🎉 所有测试完成！")
    print("💡 如果测试通过，可以使用main_v8_all.py进行完整的GUI测试")

if __name__ == "__main__":
    main()
