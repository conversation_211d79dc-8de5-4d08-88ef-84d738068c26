#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的API客户端 - 控制RTSP垃圾检测系统
"""

import requests
import json
import sys

class RTSPDetectionClient:
    def __init__(self, base_url="http://192.168.0.138:5000"):
        self.base_url = base_url
    
    def start_detection(self):
        """启动检测"""
        try:
            response = requests.post(f"{self.base_url}/api/start", timeout=5)
            result = response.json()
            print(f"✅ {result['message']}")
            return True
        except Exception as e:
            print(f"❌ 启动检测失败: {e}")
            return False
    
    def stop_detection(self):
        """停止检测"""
        try:
            response = requests.post(f"{self.base_url}/api/stop", timeout=5)
            result = response.json()
            print(f"🛑 {result['message']}")
            return True
        except Exception as e:
            print(f"❌ 停止检测失败: {e}")
            return False
    
    def get_status(self):
        """获取状态"""
        try:
            response = requests.post(f"{self.base_url}/api/status", timeout=5)
            result = response.json()
            
            print("📊 系统状态:")
            print(f"  检测状态: {'🟢 启用' if result['detection_enabled'] else '🔴 禁用'}")
            print(f"  处理状态: {'🟢 活跃' if result['processing_active'] else '🔴 停止'}")
            
            stats = result['stats']
            print(f"  接收帧数: {stats['frames_received']}")
            print(f"  处理帧数: {stats['frames_processed']}")
            print(f"  有效帧数: {stats['valid_frames']}")
            print(f"  错误帧数: {stats['error_frames']}")
            
            return result
        except Exception as e:
            print(f"❌ 获取状态失败: {e}")
            return None

def main():
    if len(sys.argv) < 2:
        print("🔧 RTSP垃圾检测系统 API 客户端")
        print("使用方法:")
        print("  python api_client.py start    # 启动检测")
        print("  python api_client.py stop     # 停止检测")
        print("  python api_client.py status   # 获取状态")
        print("  python api_client.py [IP]     # 指定服务器IP")
        return
    
    command = sys.argv[1].lower()
    
    # 如果提供了IP地址
    if len(sys.argv) > 2:
        ip = sys.argv[2]
        client = RTSPDetectionClient(f"http://{ip}:5000")
    else:
        client = RTSPDetectionClient()
    
    if command == "start":
        client.start_detection()
    elif command == "stop":
        client.stop_detection()
    elif command == "status":
        client.get_status()
    else:
        print(f"❌ 未知命令: {command}")
        print("支持的命令: start, stop, status")

if __name__ == "__main__":
    main()
