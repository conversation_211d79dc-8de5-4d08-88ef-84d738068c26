#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试Web界面按钮功能的简化版本
不包含YOLO和复杂依赖，专注于验证按钮状态更新
"""

import http.server
import socketserver
import json
import threading
import time
import socket

# 全局状态变量
detection_enabled = False
processing_active = False

# 模拟统计数据
stats = {
    'frames_received': 0,
    'frames_processed': 0,
    'valid_frames': 0,
    'start_time': time.time()
}

def get_local_ip():
    """获取本地IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def simulate_processing():
    """模拟帧处理过程"""
    global stats, detection_enabled, processing_active
    
    while True:
        time.sleep(0.1)  # 10 FPS模拟
        
        # 模拟接收帧
        stats['frames_received'] += 1
        stats['valid_frames'] += 1
        
        # 只有在检测启用时才模拟处理
        if detection_enabled and processing_active:
            stats['frames_processed'] += 1
            
            # 每100帧打印一次状态
            if stats['frames_processed'] % 100 == 0:
                print(f"[SIM] 🚀 已处理 {stats['frames_processed']} 帧", flush=True)

class TestHandler(http.server.BaseHTTPRequestHandler):
    """简化的HTTP处理器"""
    
    def do_GET(self):
        if self.path == '/':
            self.send_main_page()
        else:
            self.send_error(404)
    
    def do_POST(self):
        global detection_enabled, processing_active
        
        if self.path == '/api/start':
            detection_enabled = True
            processing_active = True
            response = {"status": "success", "message": "Detection started"}
            print(f"[API] ✅ 检测启动 - detection_enabled: {detection_enabled}", flush=True)
        elif self.path == '/api/stop':
            detection_enabled = False
            processing_active = False
            response = {"status": "success", "message": "Detection stopped"}
            print(f"[API] 🛑 检测停止 - detection_enabled: {detection_enabled}", flush=True)
        elif self.path == '/api/status':
            response = {
                "status": "success",
                "detection_enabled": detection_enabled,
                "processing_active": processing_active,
                "stats": stats
            }
        else:
            response = {"status": "error", "message": "Unknown API endpoint"}
        
        # 发送JSON响应
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())
    
    def send_main_page(self):
        """发送主页"""
        local_ip = get_local_ip()
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Web按钮功能测试</title>
            <style>
                body {{ font-family: Arial, sans-serif; text-align: center; background: #f0f2f5; color: #333; margin: 0; padding: 20px; }}
                .container {{ max-width: 600px; margin: 20px auto; padding: 30px; background: #ffffff; border-radius: 12px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); }}
                h1 {{ color: #2c3e50; margin-bottom: 25px; }}
                .info {{ background: #e9f7ef; color: #28a745; padding: 15px; margin: 20px 0; border-radius: 8px; }}
                .status {{ padding: 15px; margin: 20px 0; border-radius: 5px; font-weight: bold; color: white; }}
                .controls {{ margin: 20px 0; }}
                .btn {{ padding: 12px 24px; margin: 8px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; font-weight: bold; }}
                .btn-start {{ background: #28a745; color: white; }}
                .btn-stop {{ background: #dc3545; color: white; }}
                .btn-refresh {{ background: #007bff; color: white; }}
                .stats {{ background: #f8f9fa; padding: 15px; margin: 20px 0; border-radius: 8px; text-align: left; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🧪 Web按钮功能测试</h1>
                <div class="info">
                    <p>本地IP: {local_ip}:5000</p>
                    <p>专门测试按钮状态更新功能</p>
                </div>
                <div class="status" id="status">检测状态: 加载中...</div>
                <div class="controls">
                    <button class="btn btn-start" onclick="startDetection()">🚀 启动检测</button>
                    <button class="btn btn-stop" onclick="stopDetection()">🛑 停止检测</button>
                    <button class="btn btn-refresh" onclick="updateStatus()">🔄 刷新状态</button>
                </div>
                <div class="stats" id="stats">统计信息: 加载中...</div>
            </div>
            
            <script>
                function updateStatus() {{
                    console.log('🔄 更新状态...');
                    fetch('/api/status', {{method: 'POST'}})
                        .then(response => response.json())
                        .then(data => {{
                            const statusDiv = document.getElementById('status');
                            const statsDiv = document.getElementById('stats');
                            
                            if (data.detection_enabled) {{
                                statusDiv.style.background = '#28a745';
                                statusDiv.innerHTML = '检测状态: <strong>🟢 启用中</strong>';
                                console.log('✅ 状态更新为: 启用');
                            }} else {{
                                statusDiv.style.background = '#dc3545';
                                statusDiv.innerHTML = '检测状态: <strong>🔴 禁用</strong>';
                                console.log('❌ 状态更新为: 禁用');
                            }}
                            
                            // 更新统计信息
                            statsDiv.innerHTML = `
                                <strong>📊 实时统计:</strong><br>
                                接收帧数: ${{data.stats.frames_received}}<br>
                                处理帧数: ${{data.stats.frames_processed}}<br>
                                有效帧数: ${{data.stats.valid_frames}}<br>
                                检测启用: ${{data.detection_enabled ? '是' : '否'}}<br>
                                处理激活: ${{data.processing_active ? '是' : '否'}}
                            `;
                        }})
                        .catch(error => {{
                            console.error('❌ 状态更新失败:', error);
                            document.getElementById('status').innerHTML = '检测状态: <strong>❌ 更新失败</strong>';
                        }});
                }}
                
                function startDetection() {{
                    console.log('🚀 启动检测...');
                    fetch('/api/start', {{method: 'POST'}})
                        .then(response => response.json())
                        .then(data => {{
                            alert('✅ ' + data.message);
                            updateStatus();
                        }})
                        .catch(error => {{
                            alert('❌ 启动失败: ' + error);
                        }});
                }}
                
                function stopDetection() {{
                    console.log('🛑 停止检测...');
                    fetch('/api/stop', {{method: 'POST'}})
                        .then(response => response.json())
                        .then(data => {{
                            alert('✅ ' + data.message);
                            updateStatus();
                        }})
                        .catch(error => {{
                            alert('❌ 停止失败: ' + error);
                        }});
                }}
                
                // 页面加载时立即更新状态
                document.addEventListener('DOMContentLoaded', function() {{
                    console.log('📄 页面加载完成，初始化状态...');
                    updateStatus();
                    // 每2秒自动更新状态
                    setInterval(updateStatus, 2000);
                }});
            </script>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode())
    
    def log_message(self, fmt, *args):
        # 抑制HTTP访问日志
        pass

def main():
    """主函数"""
    PORT = 5000
    
    print("🧪 启动Web按钮功能测试服务器")
    print(f"📺 访问: http://{get_local_ip()}:{PORT}")
    print("=" * 50)
    
    # 启动模拟处理线程
    threading.Thread(target=simulate_processing, daemon=True).start()
    print("[THREAD] 模拟处理线程已启动")
    
    # 启动HTTP服务器
    try:
        with socketserver.TCPServer(("", PORT), TestHandler) as httpd:
            print(f"✅ 服务器启动成功，端口: {PORT}")
            print("💡 现在可以测试Web界面按钮功能")
            httpd.serve_forever()
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

if __name__ == "__main__":
    main()
