#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebRTC流垃圾检测 + HTTP推流（基于WHIP/WHEP协议）
- 通过WHIP/WHEP协议获取WebRTC流
- YOLOv8垃圾检测 (GPU加速)
- HTTP MJPEG流推送处理后的视频
"""

import cv2
import torch
import numpy as np
from ultralytics import YOL<PERSON>
from threading import Thread, Lock
from queue import Queue, Empty
from time import time, sleep
import os
import warnings
import socket
import http.server
from functools import partial
import requests
from urllib.parse import urljoin
import json
import asyncio
from aiortc import RTCPeerConnection, VideoStreamTrack, RTCSessionDescription
from aiortc.contrib.signaling import TcpSocketSignaling

# 环境配置，抑制日志输出
os.environ['OPENCV_LOG_LEVEL'] = 'SILENT'
os.environ['OPENCV_VIDEOIO_DEBUG'] = '0'
os.environ['FFMPEG_LOG_LEVEL'] = 'quiet'
os.environ['AV_LOG_FORCE_NOCOLOR'] = '1'
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

warnings.filterwarnings('ignore')
cv2.setLogLevel(0)

# 检测GPU是否可用
device = 'cuda' if torch.cuda.is_available() else 'cpu'

print("=" * 50)
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA device count: {torch.cuda.device_count()}")
    print(f"CUDA device name: {torch.cuda.get_device_name(0)}")
    print(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
print(f"Using device: {device}")
print(f"OpenCV version: {cv2.__version__}")
print("=" * 50)

class Config:
    # WebRTC流配置（基于WHIP/WHEP协议）
    WEBRTC_SIGNALING_URL = "http://*************:8889/camera/whep"  # WHEP信令端点
    ICE_SERVERS = [  # ICE服务器配置
        {"urls": "stun:stun.l.google.com:19302"}
    ]
    
    # 模型配置
    CONFIDENCE_THRESHOLD = 0.5  # 检测置信度阈值
    MODEL_PATH = 'yolov8n.pt'   # 模型路径
    
    # 队列配置
    QUEUE_SIZE = 3              # 帧队列大小
    STATS_INTERVAL = 30         # 统计信息输出间隔(秒)
    HTTP_FPS = 20               # HTTP流帧率
    
    # HTTP流配置
    STREAM_CONFIGS = [
        {
            "name": "Detection Result Stream", 
            "port": 5000, 
            "path": "/video_feed", 
            "source": "processed"
        }
    ]
    ENABLE_DISPLAY = False  # 关闭显示

# 初始化全局变量
garbage_classes = {}
CONFIDENCE_THRESHOLD = Config.CONFIDENCE_THRESHOLD
QUEUE_SIZE = Config.QUEUE_SIZE
STATS_INTERVAL = Config.STATS_INTERVAL

# 加载YOLOv8模型
model = YOLO(Config.MODEL_PATH)
if device == 'cuda':
    model.to(device)
    print(f"[DEBUG] Model loaded to GPU: {device}")
else:
    print(f"[DEBUG] Model loaded to CPU: {device}")
print("[DEBUG] Model loaded successfully")

# 获取模型支持的类别
Config.GARBAGE_CLASSES = model.names
garbage_classes = Config.GARBAGE_CLASSES
print(f"[INFO] All categories supported by the model: {Config.GARBAGE_CLASSES}")

# 全局帧变量和锁
current_processed_frame = None
current_raw_frame = None
frame_lock = Lock()
frame_queue = Queue(maxsize=QUEUE_SIZE)

# 统计信息
stats = {
    'frames_received': 0,
    'frames_processed': 0,
    'frames_displayed': 0,
    'valid_frames': 0,
    'error_frames': 0,
    'queue_drops': 0,
    'start_time': time()
}

class WebRTCVideoTrack(VideoStreamTrack):
    """
    WebRTC视频轨道，用于接收远程视频流
    继承自aiortc的VideoStreamTrack
    """
    def __init__(self):
        super().__init__()
        self.frame_buffer = None
        self.frame_lock = Lock()

    def put_frame(self, frame):
        """更新帧缓冲区"""
        with self.frame_lock:
            self.frame_buffer = frame

    async def recv(self):
        """获取视频帧"""
        pts, time_base = await self.next_timestamp()
        
        with self.frame_lock:
            frame = self.frame_buffer
            
        if frame is None:
            return None
            
        # 转换为aiortc需要的格式
        frame = frame.astype('uint8')
        return frame

def get_local_ip():
    """获取本地IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.settimeout(2)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception as e:
        print(f"[WARNING] Failed to get IP: {e}, using default IP address 127.0.0.1")
        return "127.0.0.1"

def create_test_frame():
    """创建测试帧，当无法获取视频流时使用"""
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    cv2.putText(frame, "WebRTC Stream Not Available", (150, 200),
                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    cv2.putText(frame, "Showing Test Pattern", (200, 250),
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
    cv2.putText(frame, f"Time: {int(time())}", (250, 300),
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    cv2.circle(frame, (320, 350), 50, (0, 0, 255), 3)
    cv2.rectangle(frame, (250, 400), (390, 450), (255, 0, 0), 3)
    return frame

def is_frame_valid(frame):
    """检查帧是否有效"""
    if frame is None or frame.size == 0:
        return False
    if len(frame.shape) != 3 or frame.shape[2] != 3:
        return False
    if frame.shape[0] < 100 or frame.shape[1] < 100:
        return False
    mean_val = np.mean(frame)
    if mean_val < 10 or mean_val > 245:
        return False
    return True

def draw_detections(frame, detections):
    """在帧上绘制检测结果"""
    for x1, y1, x2, y2, label in detections:
        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
        cv2.rectangle(frame, (x1, y1 - label_size[1] - 10),
                      (x1 + label_size[0], y1), (0, 255, 0), -1)
        cv2.putText(frame, label, (x1, y1 - 5),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    return frame

class DynamicStreamingHandler(http.server.BaseHTTPRequestHandler):
    """HTTP流处理器"""
    def __init__(self, *args, source_type, **kwargs):
        self.source_type = source_type
        super().__init__(*args, **kwargs)

    def do_GET(self):
        global current_processed_frame

        if self.path == '/':
            # 主页HTML
            self.send_response(200)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.end_headers()

            local_ip = get_local_ip()
            stream_url = f"http://{local_ip}:{Config.STREAM_CONFIGS[0]['port']}{Config.STREAM_CONFIGS[0]['path']}"
            
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>WebRTC Garbage Detection Stream</title>
                <style>
                    body {{ font-family: Arial, sans-serif; text-align: center; }}
                    .container {{ max-width: 800px; margin: 0 auto; }}
                    img {{ max-width: 100%; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>WebRTC Garbage Detection Stream</h1>
                    <p>Local IP: {local_ip}</p>
                    <img src="{stream_url}" alt="Detection Stream">
                </div>
            </body>
            </html>
            """
            self.wfile.write(html.encode())

        elif self.path == '/video_feed':
            # 视频流端点
            self.send_response(200)
            self.send_header('Content-Type', 'multipart/x-mixed-replace; boundary=frame')
            self.send_header('Cache-Control', 'no-cache')
            self.send_header('Connection', 'close')
            self.end_headers()

            frame_count = 0
            while True:
                try:
                    frame_to_send = None
                    with frame_lock:
                        if self.source_type == "processed":
                            frame_to_send = current_processed_frame.copy() if current_processed_frame is not None else None

                    if frame_to_send is None:
                        frame_to_send = create_test_frame()
                        cv2.putText(frame_to_send, f"Frame: {frame_count}", 
                                   (20, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 2)

                    ret, buffer = cv2.imencode('.jpg', frame_to_send, [cv2.IMWRITE_JPEG_QUALITY, 85])
                    if ret:
                        frame_bytes = buffer.tobytes()
                        self.wfile.write(b'--frame\r\n')
                        self.wfile.write(b'Content-Type: image/jpeg\r\n')
                        self.wfile.write(f'Content-Length: {len(frame_bytes)}\r\n\r\n'.encode())
                        self.wfile.write(frame_bytes)
                        self.wfile.write(b'\r\n')
                        self.wfile.flush()

                        frame_count += 1

                    sleep(1.0 / Config.HTTP_FPS)

                except (ConnectionResetError, BrokenPipeError):
                    break
                except Exception as e:
                    print(f"[HTTP] Stream error: {e}")
                    break
        else:
            self.send_response(404)
            self.end_headers()

    def log_message(self, format, *args):
        pass  # 抑制日志输出

async def webrtc_receiver(video_track):
    """
    WebRTC接收器主循环
    :param video_track: WebRTC视频轨道
    """
    pc = RTCPeerConnection()
    
    # 添加ICE服务器
    for ice_server in Config.ICE_SERVERS:
        pc.addIceServer(ice_server)
    
    # 添加视频轨道
    pc.addTrack(video_track)
    
    # 创建并发送Offer
    offer = await pc.createOffer()
    await pc.setLocalDescription(offer)
    
    # 通过WHIP/WHEP协议发送Offer
    headers = {
        'Content-Type': 'application/sdp'
    }
    try:
        response = requests.post(
            Config.WEBRTC_SIGNALING_URL,
            data=pc.localDescription.sdp,
            headers=headers
        )
        
        if response.status_code == 201:
            # 处理Answer
            answer = RTCSessionDescription(
                sdp=response.text,
                type='answer'
            )
            await pc.setRemoteDescription(answer)
            print("[WebRTC] Connection established")
        else:
            print(f"[WebRTC] Server error: {response.status_code}")
    except Exception as e:
        print(f"[WebRTC] Signaling error: {e}")
    
    # 保持连接
    while True:
        await asyncio.sleep(1)

def process_frames():
    """帧处理线程函数"""
    global current_processed_frame

    while True:
        try:
            frame = frame_queue.get(timeout=5)

            if not is_frame_valid(frame):
                stats['error_frames'] += 1
                continue

            stats['valid_frames'] += 1

            # 使用YOLOv8进行目标检测
            results = model(frame, imgsz=416, conf=CONFIDENCE_THRESHOLD, verbose=False, device=device)[0]
            detections = []

            # 解析检测结果
            for box in results.boxes:
                class_id = int(box.cls)
                if class_id in Config.GARBAGE_CLASSES:
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    confidence = float(box.conf)
                    label = f"{Config.GARBAGE_CLASSES[class_id]} {confidence:.2f}"
                    detections.append((x1, y1, x2, y2, label))

            # 绘制检测结果
            processed_frame = draw_detections(frame.copy(), detections)
            cv2.putText(processed_frame, f"Objects: {len(detections)} | Device: {device}",
                        (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)

            with frame_lock:
                current_processed_frame = processed_frame

            stats['frames_processed'] += 1

        except Empty:
            print("[WARNING] Frame queue empty")
            continue
        except Exception as e:
            print(f"[PROCESS_THREAD] Error: {e}")
            break

def start_http_server(port, handler_class, server_name="HTTP Server"):
    """启动HTTP服务器"""
    try:
        from http.server import HTTPServer
        httpd = HTTPServer(("0.0.0.0", port), handler_class)
        local_ip = get_local_ip()
        print(f"[{server_name}] Server started on port {port}")
        print(f"[{server_name}] Access URL: http://{local_ip}:{port}/")
        
        while True:
            httpd.handle_request()
    except Exception as e:
        print(f"[{server_name}] Error: {e}")

async def webrtc_capture_loop(video_track):
    """WebRTC捕获线程"""
    while True:
        try:
            # 从WebRTC轨道获取帧
            frame = await video_track.recv()
            if frame is not None:
                # 转换为OpenCV格式
                frame = frame.to_ndarray(format="bgr24")
                
                # 更新帧缓冲区
                with frame_lock:
                    current_raw_frame = frame.copy()
                
                # 放入处理队列
                if not frame_queue.full():
                    frame_queue.put_nowait(frame)
                    stats['frames_received'] += 1
                else:
                    stats['queue_drops'] += 1
        except Exception as e:
            print(f"[WebRTC] Capture error: {e}")
            await asyncio.sleep(1)

async def main_async():
    """异步主函数"""
    # 创建WebRTC视频轨道
    video_track = WebRTCVideoTrack()
    
    # 启动WebRTC接收器
    webrtc_task = asyncio.create_task(webrtc_receiver(video_track))
    
    # 启动帧捕获
    capture_task = asyncio.create_task(webrtc_capture_loop(video_track))
    
    # 启动HTTP服务器
    handler = partial(DynamicStreamingHandler, source_type="processed")
    http_thread = Thread(target=start_http_server, args=(5000, handler))
    http_thread.start()
    
    # 启动帧处理线程
    process_thread = Thread(target=process_frames)
    process_thread.start()
    
    # 主循环
    last_stats_time = time()
    while True:
        await asyncio.sleep(1)
        
        # 打印统计信息
        if time() - last_stats_time >= STATS_INTERVAL:
            print_stats()
            last_stats_time = time()

def print_stats():
    """打印统计信息"""
    elapsed = time() - stats['start_time']
    fps_received = stats['frames_received'] / elapsed if elapsed > 0 else 0
    fps_processed = stats['frames_processed'] / elapsed if elapsed > 0 else 0
    
    print(f"\n====== Statistics ======")
    print(f"Frames Received: {stats['frames_received']} ({fps_received:.1f} fps)")
    print(f"Frames Processed: {stats['frames_processed']} ({fps_processed:.1f} fps)")
    print(f"Queue Drops: {stats['queue_drops']}")
    print("=======================")

def main():
    """主函数入口"""
    try:
        asyncio.run(main_async())
    except KeyboardInterrupt:
        print("\n[MAIN] Exiting...")
    except Exception as e:
        print(f"[MAIN] Error: {e}")

if __name__ == "__main__":
    main()