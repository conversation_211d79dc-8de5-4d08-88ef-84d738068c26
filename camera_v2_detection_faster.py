import cv2
import torch
import numpy as np
from ultralytics import YOL<PERSON>
from threading import Thread
from queue import Queue, Empty
from time import time
import sys

# 初始化YOLOv8模型
MODEL_PATH = 'yolov8n.pt'  # 使用最小的模型以提高速度
print(f"[INFO] 正在加载模型: {MODEL_PATH}")
try:
    model = YOLO(MODEL_PATH)
    print("[INFO] 模型加载成功")
except Exception as e:
    print(f"[ERROR] 模型加载失败: {e}")
    sys.exit(1)

# 定义要检测的垃圾类别ID（COCO数据集类别映射）
GARBAGE_CLASSES = {
    39: 'bottle',  # 瓶子
    67: 'cell phone'  # 手机
}

# 性能优化参数 - 更激进的优化设置
FRAME_BUFFER_SIZE = 1  # 减小队列大小以降低延迟
CONFIDENCE_THRESHOLD = 0.6  # 提高置信度阈值减少误检
PROCESS_FRAME_WIDTH = 320  # 进一步降低处理分辨率
SKIP_FRAMES = 4  # 增加跳帧数量
DISPLAY_FPS_INTERVAL = 1.0  # 降低FPS更新频率

# 多线程处理队列
frame_queue = Queue(maxsize=FRAME_BUFFER_SIZE)
results_queue = Queue(maxsize=FRAME_BUFFER_SIZE)

# 统计信息
stats = {
    'frames_received': 0,
    'frames_processed': 0,
    'frames_displayed': 0,
    'start_time': time()
}

# 控制变量
processing_active = True
frame_count = 0
last_detection = None  # 存储最后一次检测结果


def process_frames():
    """多线程处理帧"""
    global processing_active, last_detection
    
    while processing_active:
        try:
            # 获取帧，设置超时避免永久阻塞
            frame = frame_queue.get(timeout=0.5)
            if frame is None:  # 终止信号
                break
                
            # 调整图像大小以加快处理速度
            h, w = frame.shape[:2]
            new_w = PROCESS_FRAME_WIDTH
            new_h = int(h * (new_w / w))
            small_frame = cv2.resize(frame, (new_w, new_h))
            
            # 使用YOLOv8进行检测 - 使用半精度浮点数加速
            with torch.no_grad():  # 禁用梯度计算
                results = model(small_frame, imgsz=new_w, conf=CONFIDENCE_THRESHOLD, verbose=False)[0]
            
            # 提取检测结果并调整回原始尺寸的坐标
            detections = []
            for box in results.boxes:
                class_id = int(box.cls)
                if class_id in GARBAGE_CLASSES:
                    # 将坐标转换回原始图像尺寸
                    x1, y1, x2, y2 = map(float, box.xyxy[0])
                    x1 = int(x1 * (w / new_w))
                    y1 = int(y1 * (h / new_h))
                    x2 = int(x2 * (w / new_w))
                    y2 = int(y2 * (h / new_h))
                    
                    conf = float(box.conf)
                    label = f"{GARBAGE_CLASSES[class_id]} {conf:.2f}"
                    detections.append((x1, y1, x2, y2, label))
            
            # 更新最后一次检测结果
            last_detection = (frame, detections)
            
            # 统计处理帧数
            stats['frames_processed'] += 1
            
            # 放入结果队列，如果队列满则丢弃旧结果
            if not results_queue.full():
                results_queue.put((frame, detections))
            
        except Empty:
            # 超时是正常的，继续等待
            continue
        except Exception as e:
            print(f"[ERROR] 处理异常: {e}")
            continue


def draw_detections(frame, detections):
    """在帧上绘制检测框"""
    for (x1, y1, x2, y2, label) in detections:
        # 绘制矩形框
        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        # 添加标签（使用更高效的方式）
        cv2.putText(frame, label, (x1, y1 - 10),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
    return frame


# 启动处理线程
processing_thread = Thread(target=process_frames)
processing_thread.daemon = True
processing_thread.start()

# 主处理循环
camera_id = 0  # 通常0表示默认摄像头
print(f"[INFO] 启动摄像头 ID: {camera_id}")
fps_counter = 0
fps_timer = time()
fps_value = 0

# 创建摄像头捕获对象并设置属性
cap = cv2.VideoCapture(camera_id)
if not cap.isOpened():
    print(f"[ERROR] 无法打开摄像头 {camera_id}")
    sys.exit(1)

# 尝试设置摄像头属性以提高性能
cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
cap.set(cv2.CAP_PROP_FPS, 30)  # 尝试设置更高的FPS
cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 减少缓冲区大小

try:
    while True:
        # 读取一帧
        ret, frame = cap.read()
        if not ret:
            print("[WARNING] 无法获取帧")
            continue
        
        # 更新统计信息
        stats['frames_received'] += 1
        frame_count += 1
        
        # 跳帧处理：只处理部分帧以减轻计算负担
        if frame_count % SKIP_FRAMES == 0:
            # 如果队列未满，放入当前帧进行处理
            if not frame_queue.full():
                frame_queue.put(frame.copy())
        
        # 尝试从结果队列获取处理好的帧（非阻塞）
        display_frame = frame  # 直接使用原始帧，避免复制
        try:
            processed_frame, detections = results_queue.get_nowait()
            # 直接在原始帧上绘制，避免复制
            draw_detections(display_frame, detections)
            stats['frames_displayed'] += 1
        except Empty:
            # 结果队列为空，使用最后一次检测结果（如果有）
            if last_detection is not None:
                _, last_detections = last_detection
                draw_detections(display_frame, last_detections)
        
        # 计算并显示FPS
        fps_counter += 1
        current_time = time()
        if current_time - fps_timer >= DISPLAY_FPS_INTERVAL:
            fps_value = fps_counter / (current_time - fps_timer)
            fps_counter = 0
            fps_timer = current_time
        
        # 显示FPS
        cv2.putText(display_frame, f"FPS: {fps_value:.1f}", (10, 30),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        
        # 显示实时结果
        cv2.imshow('Garbage Detection', display_frame)
        
        # 按'q'退出，使用更短的等待时间
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

except KeyboardInterrupt:
    print("[INFO] 程序被用户中断")
except Exception as e:
    print(f"[ERROR] 主循环异常: {e}")
    import traceback
    traceback.print_exc()
finally:
    # 设置处理线程终止标志
    processing_active = False
    
    # 发送终止信号
    frame_queue.put(None)
    
    # 等待线程结束
    processing_thread.join(timeout=2.0)
    
    # 释放摄像头
    cap.release()
    
    # 关闭窗口
    cv2.destroyAllWindows()
    
    # 打印运行时间
    elapsed = time() - stats['start_time']
    print(f"[INFO] 总运行时间: {elapsed:.1f}秒")
    print(f"[INFO] 处理帧数: {stats['frames_processed']}")
    print("[INFO] 程序退出")
