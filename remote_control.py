#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RTSP垃圾检测系统远程控制示例
"""

import requests
import json
import time

# 服务器配置
SERVER_IP = "*************"  # 根据实际情况修改
SERVER_PORT = 5000
BASE_URL = f"http://{SERVER_IP}:{SERVER_PORT}"

def start_detection():
    """启动检测"""
    try:
        response = requests.post(f"{BASE_URL}/api/start", timeout=5)
        result = response.json()
        print(f"启动检测结果: {result['message']}")
        return result
    except Exception as e:
        print(f"启动检测失败: {e}")
        return None

def stop_detection():
    """停止检测"""
    try:
        response = requests.post(f"{BASE_URL}/api/stop", timeout=5)
        result = response.json()
        print(f"停止检测结果: {result['message']}")
        return result
    except Exception as e:
        print(f"停止检测失败: {e}")
        return None

def get_status():
    """获取系统状态"""
    try:
        response = requests.post(f"{BASE_URL}/api/status", timeout=5)
        result = response.json()
        
        # 打印状态信息
        print("\n系统状态:")
        print(f"检测状态: {'启用' if result['detection_enabled'] else '禁用'}")
        print(f"处理状态: {'活跃' if result['processing_active'] else '停止'}")
        
        # 打印统计信息
        stats = result['stats']
        print(f"接收帧数: {stats['frames_received']}")
        print(f"处理帧数: {stats['frames_processed']}")
        print(f"有效帧数: {stats['valid_frames']}")
        print(f"错误帧数: {stats['error_frames']}")
        
        return result
    except Exception as e:
        print(f"获取状态失败: {e}")
        return None

def display():
    print("RTSP垃圾检测系统远程控制示例")
    print("=" * 50)
    
    # 获取初始状态
    print("获取初始状态...")
    initial_status = get_status()
    
    # 启动检测
    print("\n启动检测...")
    start_detection()
    
    # 等待几秒钟
    print("\n等待3秒...")
    time.sleep(3)
    
    # 获取启动后状态
    print("\n获取启动后状态...")
    running_status = get_status()
    
    # 停止检测
    print("\n停止检测...")
    stop_detection()
    
    # 获取最终状态
    print("\n获取最终状态...")
    final_status = get_status()
    
    print("\n演示完成!")

def main():
    """主函数"""
    while(True):
        number=input("please input number,0 获取 status ,1 开始检测,2 停止检测: ")
        if number == '0':
            get_status()
        elif number == '1':
            start_detection()
        elif number == '2':
            stop_detection()

if __name__ == "__main__":
    main()