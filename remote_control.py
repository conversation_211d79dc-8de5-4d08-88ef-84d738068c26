#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RTSP垃圾检测系统远程控制工具 - 支持自定义主机号和端口号
"""

import requests
import json
import time
import argparse

class RemoteController:
    def __init__(self, host_number, port=5000):
        """
        初始化远程控制器

        Args:
            host_number (str): 主机号（IP地址最后一段）
            port (int): 服务器端口号
        """
        self.server_ip = f"192.168.0.{host_number}"
        self.server_port = port
        self.base_url = f"http://{self.server_ip}:{self.server_port}"

        print(f"🎮 远程控制器初始化")
        print(f"📡 目标服务器: {self.base_url}")
        print(f"🔧 API端点: {self.base_url}/api/*")

    def test_connection(self):
        """测试连接"""
        try:
            response = requests.get(self.base_url, timeout=3)
            if response.status_code == 200:
                print(f"✅ 连接成功: {self.base_url}")
                return True
            else:
                print(f"❌ 连接失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 连接异常: {e}")
            return False

    def start_detection(self):
        """启动检测"""
        try:
            response = requests.post(f"{self.base_url}/api/start", timeout=5)
            result = response.json()
            print(f"🚀 启动检测结果: {result['message']}")
            return result
        except Exception as e:
            print(f"❌ 启动检测失败: {e}")
            return None

    def stop_detection(self):
        """停止检测"""
        try:
            response = requests.post(f"{self.base_url}/api/stop", timeout=5)
            result = response.json()
            print(f"🛑 停止检测结果: {result['message']}")
            return result
        except Exception as e:
            print(f"❌ 停止检测失败: {e}")
            return None

    def get_status(self):
        """获取系统状态"""
        try:
            response = requests.post(f"{self.base_url}/api/status", timeout=5)
            result = response.json()

            # 打印状态信息
            print("\n📊 系统状态:")
            print(f"   检测状态: {'🟢 启用' if result['detection_enabled'] else '🔴 禁用'}")
            print(f"   处理状态: {'🟢 活跃' if result['processing_active'] else '🔴 停止'}")

            # 打印系统信息
            if 'system_info' in result:
                sys_info = result['system_info']
                print(f"   计算设备: {sys_info.get('device', 'unknown').upper()}")
                print(f"   模型状态: {'✅ 已加载' if sys_info.get('model_loaded', False) else '❌ 未加载'}")
                print(f"   RTSP连接: {'✅ 正常' if sys_info.get('rtsp_connected', False) else '❌ 断开'}")

            # 打印统计信息
            stats = result['stats']
            print(f"📈 统计信息:")
            print(f"   接收帧数: {stats['frames_received']}")
            print(f"   处理帧数: {stats['frames_processed']}")
            print(f"   有效帧数: {stats['valid_frames']}")
            print(f"   错误帧数: {stats['error_frames']}")

            # 如果有检测统计，也显示
            if 'detections_total' in stats:
                print(f"   总检测数: {stats['detections_total']}")
                print(f"   当前检测: {stats.get('detections_current', 0)}")

            # 显示FPS信息
            elapsed = stats.get('elapsed_time', 0)
            if elapsed > 0:
                fps_received = stats['frames_received'] / elapsed
                fps_processed = stats['frames_processed'] / elapsed
                print(f"   接收FPS: {fps_received:.1f}")
                print(f"   处理FPS: {fps_processed:.1f}")

            return result
        except Exception as e:
            print(f"❌ 获取状态失败: {e}")
            return None

    def run_demo(self):
        """运行演示"""
        print("\n🎬 RTSP垃圾检测系统远程控制演示")
        print("=" * 60)

        # 测试连接
        print("🔗 测试连接...")
        if not self.test_connection():
            print("❌ 无法连接到服务器，请检查主机号和端口号")
            return

        # 获取初始状态
        print("\n📊 获取初始状态...")
        initial_status = self.get_status()

        # 启动检测
        print("\n🚀 启动检测...")
        self.start_detection()

        # 等待几秒钟
        print("\n⏳ 等待3秒...")
        time.sleep(3)

        # 获取启动后状态
        print("\n📊 获取启动后状态...")
        running_status = self.get_status()

        # 停止检测
        print("\n🛑 停止检测...")
        self.stop_detection()

        # 获取最终状态
        print("\n📊 获取最终状态...")
        final_status = self.get_status()

        print("\n🎉 演示完成!")

    def interactive_control(self):
        """交互式控制"""
        print(f"\n🎮 交互式远程控制")
        print(f"📡 目标服务器: {self.base_url}")
        print("=" * 50)

        # 测试连接
        if not self.test_connection():
            print("❌ 无法连接到服务器，请检查主机号和端口号")
            return

        while True:
            print("\n📋 控制选项:")
            print("  0 - 获取系统状态")
            print("  1 - 启动检测")
            print("  2 - 停止检测")
            print("  3 - 运行演示")
            print("  q - 退出")

            try:
                choice = input("\n请选择操作 (0/1/2/3/q): ").strip().lower()

                if choice == '0':
                    self.get_status()
                elif choice == '1':
                    self.start_detection()
                elif choice == '2':
                    self.stop_detection()
                elif choice == '3':
                    self.run_demo()
                elif choice == 'q':
                    print("👋 退出远程控制")
                    break
                else:
                    print("❌ 无效选择，请重新输入")

            except KeyboardInterrupt:
                print("\n👋 用户中断，退出程序")
                break
            except EOFError:
                print("\n👋 输入结束，退出程序")
                break

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="RTSP垃圾检测系统远程控制工具")
    parser.add_argument("host_number", nargs='?', help="主机号（IP地址最后一段）")
    parser.add_argument("--port", type=int, default=5000, help="服务器端口号")
    parser.add_argument("--demo", action="store_true", help="运行演示模式")

    args = parser.parse_args()

    # 如果没有参数，进入交互模式
    if args.host_number is None:
        print("🎮 RTSP垃圾检测系统远程控制工具")
        print("=" * 50)
        host_number = input("请输入主机号（IP地址最后一段，如186）: ")
        port = input(f"请输入端口号（默认5000）: ") or "5000"

        try:
            port = int(port)
        except ValueError:
            print("❌ 端口号必须是数字，使用默认端口5000")
            port = 5000

        controller = RemoteController(host_number, port)

        if input("是否运行演示模式？(y/N): ").strip().lower() == 'y':
            controller.run_demo()
        else:
            controller.interactive_control()
    else:
        controller = RemoteController(args.host_number, args.port)

        if args.demo:
            controller.run_demo()
        else:
            controller.interactive_control()

if __name__ == "__main__":
    main()