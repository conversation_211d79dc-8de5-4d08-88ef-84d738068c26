#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本 - 测试RTSP垃圾检测系统的API控制功能
"""

import requests
import json
import time

# 配置
API_BASE_URL = "http://*************:5000"  # 根据实际IP调整

def test_api_endpoint(endpoint, method="POST", data=None):
    """测试API端点"""
    url = f"{API_BASE_URL}{endpoint}"
    
    try:
        if method == "POST":
            response = requests.post(url, json=data, timeout=5)
        else:
            response = requests.get(url, timeout=5)
        
        print(f"[{method}] {endpoint}")
        print(f"Status Code: {response.status_code}")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
        else:
            print(f"Response: {response.text[:200]}...")
        
        print("-" * 50)
        return response.status_code == 200
        
    except Exception as e:
        print(f"[ERROR] {endpoint}: {e}")
        print("-" * 50)
        return False

def main():
    print("🔧 RTSP垃圾检测系统 API 测试")
    print("=" * 50)
    
    # 测试主页
    print("1. 测试主页访问...")
    test_api_endpoint("/", method="GET")
    
    # 测试获取状态
    print("2. 测试获取状态...")
    test_api_endpoint("/api/status")
    
    # 测试启动检测
    print("3. 测试启动检测...")
    if test_api_endpoint("/api/start"):
        print("✅ 检测启动成功")
    else:
        print("❌ 检测启动失败")
    
    # 等待一下
    print("等待3秒...")
    time.sleep(3)
    
    # 再次获取状态
    print("4. 检测启动后的状态...")
    test_api_endpoint("/api/status")
    
    # 测试停止检测
    print("5. 测试停止检测...")
    if test_api_endpoint("/api/stop"):
        print("✅ 检测停止成功")
    else:
        print("❌ 检测停止失败")
    
    # 最终状态
    print("6. 检测停止后的状态...")
    test_api_endpoint("/api/status")
    
    print("\n🎯 API测试完成")
    print(f"🌐 Web界面: {API_BASE_URL}/")
    print(f"📹 视频流: {API_BASE_URL}/video_feed")

if __name__ == "__main__":
    main()
