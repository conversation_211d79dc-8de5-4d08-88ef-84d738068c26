#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RTSP垃圾检测系统 - 最终版本
结合Web界面状态更新和RTSP流处理
"""

import os
import cv2
import numpy as np
import http.server
import socketserver
import json
import threading
import time
from threading import Lock
from queue import Queue, Empty
from ultralytics import YOLO
import torch

# 环境配置 - 抑制日志
os.environ['OPENCV_LOG_LEVEL'] = 'SILENT'
os.environ['OPENCV_VIDEOIO_DEBUG'] = '0'
os.environ['FFMPEG_LOG_LEVEL'] = 'quiet'
os.environ['PYTHONWARNINGS'] = 'ignore'

# 配置类
class Config:
    # RTSP流配置
    RTSP_URL = "rtsp://192.168.0.186:8554/camera"
    RTSP_BUFFER_SIZE = 1
    RTSP_TIMEOUT = 10
    RTSP_RETRY_MAX = 3
    
    # 检测配置
    CONFIDENCE_THRESHOLD = 0.5
    MODEL_PATH = 'yolov8n.pt'
    
    # HTTP配置
    HTTP_PORT = 5000
    
    # 性能配置
    QUEUE_SIZE = 3
    HTTP_FPS = 20

# 全局变量
current_raw_frame = None
current_processed_frame = None
frame_lock = Lock()
frame_queue = Queue(maxsize=Config.QUEUE_SIZE)

# API控制变量
detection_enabled = False
processing_active = False

# 统计信息
stats = {
    'frames_received': 0,
    'frames_processed': 0,
    'valid_frames': 0,
    'error_frames': 0,
    'queue_drops': 0,
    'start_time': time.time()
}

# 设备检测
device = 'cuda' if torch.cuda.is_available() else 'cpu'
print(f"[INIT] 使用设备: {device}")

# 模型初始化
print("[INIT] 初始化YOLO模型...")
model = None

def load_model():
    """延迟加载模型"""
    global model
    if model is None:
        try:
            model = YOLO(Config.MODEL_PATH)
            if device == 'cuda':
                model.to(device)
            print(f"[INIT] 模型加载成功，设备: {device}")
            return True
        except Exception as e:
            print(f"[ERROR] 模型加载失败: {e}")
            return False
    return True

def get_local_ip():
    """获取本地IP地址"""
    import socket
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def create_test_frame():
    """创建测试帧"""
    frame = np.full((480, 640, 3), 128, dtype=np.uint8)
    cv2.rectangle(frame, (0, 0), (640, 120), (50, 50, 200), -1)
    cv2.putText(frame, "RTSP Test Frame", (200, 60), 
                cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 255, 255), 2)
    cv2.putText(frame, f"Time: {time.strftime('%H:%M:%S')}", (200, 100), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    return frame

def draw_detections(frame, detections):
    """在帧上绘制检测结果"""
    for detection in detections:
        x1, y1, x2, y2, conf, cls_id = detection
        
        # 获取类名
        if model is not None and hasattr(model, 'names'):
            class_name = model.names.get(int(cls_id), f"Class_{int(cls_id)}")
        else:
            class_name = f"Object_{int(cls_id)}"
        
        # 绘制边界框
        cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
        
        # 绘制标签
        label = f"{class_name}: {conf:.2f}"
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
        
        cv2.rectangle(frame, (int(x1), int(y1) - label_size[1] - 10), 
                     (int(x1) + label_size[0], int(y1)), (0, 255, 0), -1)
        
        cv2.putText(frame, label, (int(x1), int(y1) - 5), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    
    return frame

def process_frames():
    """帧处理线程"""
    global current_processed_frame, detection_enabled, processing_active
    
    while True:
        try:
            # 获取待处理帧
            frame = frame_queue.get(timeout=2)
            
            stats['valid_frames'] += 1
            
            # 只有在检测启用时才进行YOLO处理
            if detection_enabled:
                # 确保模型已加载
                if not load_model():
                    # 模型加载失败，使用原始帧
                    processed_frame = frame.copy()
                    cv2.putText(processed_frame, "Model Loading Failed", (10, 30), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                else:
                    # YOLO检测
                    results = model(frame, imgsz=416, conf=Config.CONFIDENCE_THRESHOLD, 
                                  verbose=False, device=device)[0]
                    detections = []
                    
                    # 解析检测结果
                    if hasattr(results, 'boxes') and results.boxes is not None:
                        boxes = results.boxes.xyxy.cpu().numpy()
                        confs = results.boxes.conf.cpu().numpy()
                        classes = results.boxes.cls.cpu().numpy()
                        
                        for i in range(len(boxes)):
                            x1, y1, x2, y2 = boxes[i]
                            conf = confs[i]
                            cls_id = classes[i]
                            detections.append([x1, y1, x2, y2, conf, cls_id])
                    
                    # 绘制检测结果
                    processed_frame = draw_detections(frame.copy(), detections)
                    
                    # 添加状态信息
                    cv2.putText(processed_frame, f"Objects: {len(detections)} | Device: {device} | ACTIVE",
                                (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                    
                    stats['frames_processed'] += 1
            else:
                # 检测禁用，显示原始帧
                processed_frame = frame.copy()
                cv2.putText(processed_frame, "Detection DISABLED - Use API to enable",
                            (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            # 添加IP信息
            cv2.putText(processed_frame, f"IP: {get_local_ip()}", (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
            
            # 更新全局处理帧
            with frame_lock:
                current_processed_frame = processed_frame
                
        except Empty:
            # 只有在检测启用时才打印警告
            if detection_enabled:
                print("[WARNING] 帧处理线程等待超时")
            continue
        except Exception as e:
            print(f"[ERROR] 帧处理错误: {e}")
            stats['error_frames'] += 1

class RTSPHandler(http.server.BaseHTTPRequestHandler):
    """HTTP处理器 - 结合RTSP流和状态更新"""
    
    def do_GET(self):
        global current_processed_frame, current_raw_frame, detection_enabled
        
        if self.path == '/':
            self.send_main_page()
        elif self.path == '/video_feed':
            self.send_mjpeg_stream()
        else:
            self.send_error(404)
    
    def do_POST(self):
        global detection_enabled, processing_active
        
        if self.path == '/api/start':
            detection_enabled = True
            processing_active = True
            response = {"status": "success", "message": "Detection started"}
            print(f"[API] 检测启动 - enabled: {detection_enabled}")
        elif self.path == '/api/stop':
            detection_enabled = False
            processing_active = False
            response = {"status": "success", "message": "Detection stopped"}
            print(f"[API] 检测停止 - enabled: {detection_enabled}")
        elif self.path == '/api/status':
            response = {
                "status": "success",
                "detection_enabled": detection_enabled,
                "processing_active": processing_active,
                "stats": stats
            }
        else:
            response = {"status": "error", "message": "Unknown API endpoint"}
        
        # 发送JSON响应
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())
    
    def send_main_page(self):
        """发送主页"""
        local_ip = get_local_ip()
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>RTSP垃圾检测系统</title>
            <style>
                body {{ font-family: Arial, sans-serif; text-align: center; background: #f0f2f5; color: #333; margin: 0; padding: 20px; }}
                .container {{ max-width: 800px; margin: 20px auto; padding: 30px; background: #ffffff; border-radius: 12px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); }}
                h1 {{ color: #2c3e50; margin-bottom: 25px; }}
                .info {{ background: #e9f7ef; color: #28a745; padding: 15px; margin: 20px 0; border-radius: 8px; }}
                .status {{ padding: 15px; margin: 20px 0; border-radius: 5px; font-weight: bold; }}
                .video-container {{ margin: 20px 0; }}
                img {{ border: 2px solid #ddd; border-radius: 8px; max-width: 100%; }}
                .controls {{ margin: 20px 0; }}
                .btn {{ padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }}
                .btn-start {{ background: #28a745; color: white; }}
                .btn-stop {{ background: #dc3545; color: white; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🗑️ RTSP垃圾检测系统</h1>
                <div class="info">
                    <p>RTSP源: {Config.RTSP_URL}</p>
                    <p>本地IP: {local_ip}</p>
                    <p>设备: {device}</p>
                </div>
                <div id="status" class="status">状态: 加载中...</div>
                <div class="video-container">
                    <img src="/video_feed" alt="RTSP视频流">
                </div>
                <div class="controls">
                    <button class="btn btn-start" onclick="startDetection()">启动检测</button>
                    <button class="btn btn-stop" onclick="stopDetection()">停止检测</button>
                    <button class="btn" onclick="updateStatus()">刷新状态</button>
                </div>
            </div>
            
            <script>
                function updateStatus() {{
                    fetch('/api/status', {{method: 'POST'}})
                        .then(response => response.json())
                        .then(data => {{
                            const statusDiv = document.getElementById('status');
                            
                            if (data.detection_enabled) {{
                                statusDiv.style.background = '#28a745';
                                statusDiv.style.color = 'white';
                                statusDiv.innerHTML = '状态: <strong>🟢 检测启用</strong>';
                            }} else {{
                                statusDiv.style.background = '#dc3545';
                                statusDiv.style.color = 'white';
                                statusDiv.innerHTML = '状态: <strong>🔴 检测禁用</strong>';
                            }}
                            
                            console.log('状态更新:', data);
                        }})
                        .catch(error => {{
                            console.error('状态更新失败:', error);
                            document.getElementById('status').innerHTML = '状态: ❌ 更新失败';
                        }});
                }}
                
                function startDetection() {{
                    fetch('/api/start', {{method: 'POST'}})
                        .then(response => response.json())
                        .then(data => {{
                            alert(data.message);
                            updateStatus();
                        }})
                        .catch(error => {{
                            alert('启动失败: ' + error);
                        }});
                }}
                
                function stopDetection() {{
                    fetch('/api/stop', {{method: 'POST'}})
                        .then(response => response.json())
                        .then(data => {{
                            alert(data.message);
                            updateStatus();
                        }})
                        .catch(error => {{
                            alert('停止失败: ' + error);
                        }});
                }}
                
                // 页面加载时更新状态
                window.onload = function() {{
                    updateStatus();
                    // 每5秒自动更新状态
                    setInterval(updateStatus, 5000);
                }};
            </script>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode())
    
    def send_mjpeg_stream(self):
        """发送MJPEG视频流"""
        self.send_response(200)
        self.send_header('Content-Type', 'multipart/x-mixed-replace; boundary=frame')
        self.send_header('Cache-Control', 'no-cache')
        self.send_header('Connection', 'close')
        self.end_headers()
        
        frame_count = 0
        while True:
            try:
                frame_to_send = None
                with frame_lock:
                    # 优先显示处理后的帧，如果没有则显示原始帧
                    if current_processed_frame is not None:
                        frame_to_send = current_processed_frame.copy()
                    elif current_raw_frame is not None:
                        frame_to_send = current_raw_frame.copy()
                
                # 如果没有帧，显示测试帧
                if frame_to_send is None:
                    frame_to_send = create_test_frame()
                    cv2.putText(frame_to_send, f"RTSP Stream | Frame: {frame_count}", (20, 400), 
                                cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                
                # 编码为JPEG
                ret, buffer = cv2.imencode('.jpg', frame_to_send, [cv2.IMWRITE_JPEG_QUALITY, 85])
                if ret:
                    frame_bytes = buffer.tobytes()
                    
                    self.wfile.write(b'--frame\r\n')
                    self.wfile.write(b'Content-Type: image/jpeg\r\n')
                    self.wfile.write(f'Content-Length: {len(frame_bytes)}\r\n\r\n'.encode())
                    self.wfile.write(frame_bytes)
                    self.wfile.write(b'\r\n')
                    self.wfile.flush()
                    
                    frame_count += 1
                    
                time.sleep(1.0 / Config.HTTP_FPS)

            except (ConnectionResetError, BrokenPipeError):
                break
            except Exception as e:
                if "connection" not in str(e).lower():
                    print(f"[HTTP] 流错误: {e}")
                break
    
    def log_message(self, fmt, *args):
        # 抑制HTTP日志
        pass

def rtsp_capture_thread():
    """RTSP流获取线程"""
    global current_raw_frame, processing_active

    cap = None
    reconnect_count = 0
    max_reconnects = Config.RTSP_RETRY_MAX

    while True:
        try:
            # 尝试连接RTSP流
            if cap is None or not cap.isOpened():
                print(f"[RTSP] 尝试连接: {Config.RTSP_URL}")
                cap = cv2.VideoCapture(Config.RTSP_URL)
                cap.set(cv2.CAP_PROP_BUFFERSIZE, Config.RTSP_BUFFER_SIZE)

                if not cap.isOpened():
                    reconnect_count += 1
                    print(f"[WARNING] RTSP连接失败，重试中... ({reconnect_count}/{max_reconnects})")

                    if reconnect_count >= max_reconnects:
                        print("[INFO] RTSP连接失败，使用测试模式")
                        # 使用测试帧
                        while True:
                            test_frame = create_test_frame()
                            cv2.putText(test_frame, "RTSP Connection Failed - Test Mode", (50, 200),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)

                            with frame_lock:
                                current_raw_frame = test_frame.copy()

                            # 只有在处理激活时才放入队列
                            if processing_active:
                                try:
                                    frame_queue.put_nowait(test_frame.copy())
                                except:
                                    # 队列满，清空并放入新帧
                                    try:
                                        while not frame_queue.empty():
                                            frame_queue.get_nowait()
                                            stats['queue_drops'] += 1
                                        frame_queue.put_nowait(test_frame.copy())
                                    except:
                                        pass

                            stats['frames_received'] += 1
                            time.sleep(1.0 / 10)  # 10 FPS测试模式

                    time.sleep(2)
                    continue
                else:
                    print("[RTSP] 连接成功")
                    reconnect_count = 0

            # 读取帧
            ret, frame = cap.read()

            if not ret:
                print("[WARNING] 无法读取RTSP帧，尝试重连...")
                cap.release()
                cap = None
                continue

            # 更新统计
            stats['frames_received'] += 1

            # 更新原始帧
            with frame_lock:
                current_raw_frame = frame.copy()

            # 只有在处理激活时才放入队列
            if processing_active:
                try:
                    frame_queue.put_nowait(frame.copy())
                except:
                    # 队列满，清空并放入新帧
                    try:
                        while not frame_queue.empty():
                            frame_queue.get_nowait()
                            stats['queue_drops'] += 1
                        frame_queue.put_nowait(frame.copy())
                    except:
                        pass

            # 控制帧率
            time.sleep(1.0 / 30)  # 30 FPS

        except Exception as e:
            print(f"[ERROR] RTSP流错误: {e}")
            if cap:
                cap.release()
                cap = None
            time.sleep(2)

def print_stats():
    """打印统计信息"""
    while True:
        time.sleep(30)  # 每30秒打印一次

        elapsed = time.time() - stats['start_time']
        fps_received = stats['frames_received'] / elapsed if elapsed > 0 else 0
        fps_processed = stats['frames_processed'] / elapsed if elapsed > 0 else 0

        print("\n" + "=" * 60)
        print("📊 RTSP垃圾检测系统统计")
        print("=" * 60)
        print(f"运行时间: {elapsed:.1f}秒 | 设备: {device}")
        print(f"检测状态: {'🟢 启用' if detection_enabled else '🔴 禁用'}")
        print(f"帧统计: 接收{stats['frames_received']}({fps_received:.1f}FPS) 处理{stats['frames_processed']}({fps_processed:.1f}FPS)")
        print(f"有效帧: {stats['valid_frames']} | 错误帧: {stats['error_frames']} | 队列丢弃: {stats['queue_drops']}")
        print("=" * 60 + "\n")

def main():
    """主函数"""
    global current_raw_frame, detection_enabled, processing_active

    print("🚀 启动RTSP垃圾检测系统")
    print("=" * 50)

    local_ip = get_local_ip()

    print(f"[MAIN] RTSP源: {Config.RTSP_URL}")
    print(f"[MAIN] 设备: {device}")
    print(f"[MAIN] 本地IP: {local_ip}")
    print(f"[MAIN] Web界面: http://{local_ip}:{Config.HTTP_PORT}")
    print(f"[MAIN] API控制:")
    print(f"    - 启动检测: POST http://{local_ip}:{Config.HTTP_PORT}/api/start")
    print(f"    - 停止检测: POST http://{local_ip}:{Config.HTTP_PORT}/api/stop")
    print(f"    - 获取状态: POST http://{local_ip}:{Config.HTTP_PORT}/api/status")

    # 启动帧处理线程
    print("[MAIN] 启动帧处理线程...")
    processing_thread = threading.Thread(target=process_frames, daemon=True)
    processing_thread.start()

    # 启动RTSP获取线程
    print("[MAIN] 启动RTSP获取线程...")
    rtsp_thread = threading.Thread(target=rtsp_capture_thread, daemon=True)
    rtsp_thread.start()

    # 启动统计线程
    print("[MAIN] 启动统计线程...")
    stats_thread = threading.Thread(target=print_stats, daemon=True)
    stats_thread.start()

    # 启动HTTP服务器
    print(f"[MAIN] 启动HTTP服务器在端口 {Config.HTTP_PORT}...")
    try:
        with socketserver.TCPServer(("", Config.HTTP_PORT), RTSPHandler) as httpd:
            print("✅ 系统启动完成！")
            print("💡 使用Web界面控制检测启动/停止")
            httpd.serve_forever()
    except Exception as e:
        print(f"❌ HTTP服务器启动失败: {e}")

if __name__ == "__main__":
    main()
