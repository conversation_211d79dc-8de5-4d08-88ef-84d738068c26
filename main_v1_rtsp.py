import cv2
import torch
import numpy as np
from ultralytics import YOL<PERSON>
from threading import Thread
from queue import Queue, Empty
from time import time, sleep
import sys
import os
import warnings

# 完全抑制H.264错误输出
os.environ['OPENCV_LOG_LEVEL'] = 'SILENT'
os.environ['OPENCV_VIDEOIO_DEBUG'] = '0'
os.environ['FFMPEG_LOG_LEVEL'] = 'quiet'
os.environ['AV_LOG_FORCE_NOCOLOR'] = '1'
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

# 重定向stderr到null（Windows）
import subprocess
import sys
if sys.platform == "win32":
    try:
        # 尝试重定向stderr到nul
        devnull = open('nul', 'w')
        sys.stderr = devnull
    except:
        pass

warnings.filterwarnings('ignore')
cv2.setLogLevel(0)

# 打印环境信息
print("=" * 50)
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"OpenCV version: {cv2.__version__}")
print("=" * 50)

# 初始化YOLOv8模型
MODEL_PATH = 'yolov8n.pt'
print(f"[DEBUG] 正在加载模型: {MODEL_PATH}")
try:
    model = YOLO(MODEL_PATH)
    print("[DEBUG] 模型加载成功！")
except Exception as e:
    print(f"[ERROR] 模型加载失败: {e}")
    sys.exit(1)

# 定义要检测的垃圾类别ID（COCO数据集类别映射）
GARBAGE_CLASSES = {
    39: 'bottle',  # 瓶子
    67: 'cell phone'  # 手机等
}
print(f"[DEBUG] 检测类别配置: {GARBAGE_CLASSES}")

# 性能优化参数
FRAME_BUFFER_SIZE = 3  # 帧缓冲队列大小
CONFIDENCE_THRESHOLD = 0.5  # 置信度阈值
print(f"[CONFIG] 队列大小: {FRAME_BUFFER_SIZE}, 置信度阈值: {CONFIDENCE_THRESHOLD}")

# 多线程处理队列
frame_queue = Queue(maxsize=FRAME_BUFFER_SIZE)
results_queue = Queue(maxsize=FRAME_BUFFER_SIZE)

# 调试统计
stats = {
    'frames_received': 0,
    'frames_processed': 0,
    'frames_displayed': 0,
    'queue_drops': 0,
    'valid_frames': 0,
    'error_frames': 0,
    'start_time': time()
}


def is_frame_valid(frame):
    """检查帧是否有效（针对H.264解码错误优化）"""
    if frame is None:
        return False
    
    if frame.size == 0:
        return False
    
    # 检查帧的形状
    if len(frame.shape) != 3 or frame.shape[2] != 3:
        return False
    
    height, width = frame.shape[:2]
    if width < 100 or height < 100:
        return False
    
    # 检查是否全黑或全白（H.264解码错误常见现象）
    mean_val = np.mean(frame)
    if mean_val < 5 or mean_val > 250:
        return False
    
    # 检查图像变化程度
    try:
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        std_val = np.std(gray)
        if std_val < 10:  # 标准差太小说明图像太单调
            return False
    except:
        return False
    
    return True


def process_frames():
    """多线程处理帧（优化版）"""
    print("[THREAD] 帧处理线程启动")
    while True:
        try:
            # 获取帧，设置超时避免永久阻塞
            frame = frame_queue.get(timeout=5.0)
            if frame is None:  # 终止信号
                print("[THREAD] 收到终止信号，退出处理线程")
                break

            # 验证帧有效性
            if not is_frame_valid(frame):
                stats['error_frames'] += 1
                continue

            stats['valid_frames'] += 1

            # 记录处理开始时间
            process_start = time()

            # 使用YOLO进行检测（优化参数）
            results = model(frame, imgsz=416, conf=CONFIDENCE_THRESHOLD, verbose=False)[0]

            # 提取检测结果
            detections = []
            for box in results.boxes:
                class_id = int(box.cls)
                if class_id in GARBAGE_CLASSES:
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    conf = float(box.conf)
                    
                    # 过滤太小的检测框
                    box_area = (x2 - x1) * (y2 - y1)
                    if box_area > 500:  # 最小面积阈值
                        label = f"{GARBAGE_CLASSES[class_id]} {conf:.2f}"
                        detections.append((x1, y1, x2, y2, label))

            # 统计处理帧数
            stats['frames_processed'] += 1

            # 放入结果队列
            try:
                results_queue.put_nowait((frame, detections))
            except:
                # 如果结果队列满了，丢弃最老的结果
                try:
                    results_queue.get_nowait()
                    results_queue.put_nowait((frame, detections))
                except:
                    pass

            # 打印处理信息（减少频率）
            if len(detections) > 0:
                process_time = time() - process_start
                print(f"[PROCESS] 检测到 {len(detections)} 个目标，耗时: {process_time:.3f}s")

        except Empty:
            print("[WARNING] 帧处理线程等待超时，继续等待...")
            continue
        except Exception as e:
            print(f"[ERROR] 帧处理异常: {e}")
            continue


def draw_detections(frame, detections):
    """在帧上绘制检测框（优化版）"""
    for (x1, y1, x2, y2, label) in detections:
        # 绘制矩形框
        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        
        # 绘制标签背景
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
        cv2.rectangle(frame, (x1, y1 - label_size[1] - 10), 
                     (x1 + label_size[0], y1), (0, 255, 0), -1)
        
        # 添加标签文字
        cv2.putText(frame, label, (x1, y1 - 5),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    return frame


def rtsp_stream_reader(url, max_retries=30, retry_interval=5):
    """从RTSP视频流读取帧（优化版）"""
    retries = 0
    
    while retries < max_retries:
        print(f"[STREAM] 正在连接RTSP流 ({retries + 1}/{max_retries}): {url}")
        
        try:
            # 创建VideoCapture对象
            cap = cv2.VideoCapture(url)
            
            # 设置RTSP优化参数
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)      # 最小缓冲，减少延迟
            cap.set(cv2.CAP_PROP_FPS, 15)            # 设置帧率
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)   # 设置分辨率
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            
            if not cap.isOpened():
                print("[ERROR] 无法打开RTSP流")
                retries += 1
                sleep(retry_interval)
                continue
            
            print("[STREAM] RTSP流连接成功")
            frame_count = 0
            consecutive_failures = 0
            max_consecutive_failures = 20
            
            while True:
                ret, frame = cap.read()
                
                if not ret:
                    consecutive_failures += 1
                    if consecutive_failures > max_consecutive_failures:
                        print(f"[WARNING] 连续{max_consecutive_failures}次读取失败，重新连接")
                        break
                    sleep(0.1)
                    continue
                
                consecutive_failures = 0
                stats['frames_received'] += 1
                frame_count += 1
                
                yield frame
                
                # 每200帧打印一次进度
                if frame_count % 200 == 0:
                    print(f"[STREAM] 已接收 {frame_count} 帧")
            
            cap.release()
            
            # 如果成功接收到帧，减少重试计数
            if frame_count > 0:
                print(f"[STREAM] 本次连接接收了 {frame_count} 帧，准备重连...")
                retries = max(0, retries - 1)
            else:
                print("[STREAM] 本次连接未接收到任何帧")
                retries += 1
            
            # 动态调整重连间隔
            current_interval = retry_interval + (retries * 1)
            print(f"[STREAM] 等待 {current_interval} 秒后重新连接...")
            sleep(current_interval)
            
        except Exception as e:
            print(f"[ERROR] RTSP连接异常: {e}")
            retries += 1
            sleep(retry_interval)
    
    print(f"[ERROR] 超过最大重试次数({max_retries})，RTSP流连接终止")


def print_stats():
    """打印运行统计信息（优化版）"""
    elapsed = time() - stats['start_time']
    fps_received = stats['frames_received'] / elapsed if elapsed > 0 else 0
    fps_processed = stats['frames_processed'] / elapsed if elapsed > 0 else 0
    fps_displayed = stats['frames_displayed'] / elapsed if elapsed > 0 else 0
    
    valid_rate = (stats['valid_frames'] / max(stats['frames_received'], 1)) * 100
    
    print("\n" + "=" * 60)
    print("📊 运行统计")
    print("=" * 60)
    print(f"运行时间: {elapsed:.1f}秒")
    print(f"收到帧数: {stats['frames_received']} ({fps_received:.1f} FPS)")
    print(f"有效帧数: {stats['valid_frames']} (有效率: {valid_rate:.1f}%)")
    print(f"错误帧数: {stats['error_frames']}")
    print(f"处理帧数: {stats['frames_processed']} ({fps_processed:.1f} FPS)")
    print(f"显示帧数: {stats['frames_displayed']} ({fps_displayed:.1f} FPS)")
    print(f"队列丢弃: {stats['queue_drops']}")
    print("=" * 60 + "\n")


# 启动处理线程
print("[MAIN] 启动处理线程")
processing_thread = Thread(target=process_frames)
processing_thread.daemon = True
processing_thread.start()

# 主处理循环
stream_url = "rtsp://192.168.0.186:8554/camera"  # 使用RTSP地址
print(f"[MAIN] 启动主循环，视频源: {stream_url}")
print("[MAIN] 💡 按 'q' 退出，按 's' 保存截图")

# 确保关闭所有可能存在的窗口
cv2.destroyAllWindows()

# 创建唯一的窗口名称（包含进程ID）
current_pid = os.getpid()
window_name = f'Garbage Detection (PID: {current_pid})'
print(f"[MAIN] 窗口名称: {window_name}")
print(f"[MAIN] 当前进程ID: {current_pid}")

# 检查是否有其他同名进程在运行
try:
    import subprocess
    result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'],
                          capture_output=True, text=True, shell=True)
    python_processes = result.stdout.count('python.exe')
    if python_processes > 1:
        print(f"[WARNING] 检测到 {python_processes} 个Python进程正在运行")
        print("[WARNING] 如果出现多个窗口，请关闭其他Python进程")
except:
    pass  # 如果检查失败，继续运行

fps_counter = 0
fps_timer = time()
last_stat_time = time()
frame_skip = 0
display_every_n_frames = 2  # 每2帧显示一次，减少处理负载

try:
    for frame in rtsp_stream_reader(stream_url):
        if frame is None:
            print("[WARNING] 收到空帧，跳过")
            continue

        # 尝试放入处理队列（非阻塞方式）
        try:
            # 如果队列满，尝试移除旧帧
            if frame_queue.full():
                try:
                    frame_queue.get_nowait()  # 丢弃一帧
                    stats['queue_drops'] += 1
                except Empty:
                    pass

            # 放入当前帧
            frame_queue.put_nowait(frame.copy())
        except Exception as e:
            print(f"[ERROR] 放入队列失败: {e}")

        # 跳帧显示优化
        frame_skip += 1
        if frame_skip >= display_every_n_frames:
            frame_skip = 0
            
            # 尝试从结果队列获取处理好的帧（非阻塞）
            try:
                processed_frame, detections = results_queue.get_nowait()
                frame_with_boxes = draw_detections(processed_frame, detections)

                # 计算并显示FPS
                fps_counter += 1
                current_time = time()
                if current_time - fps_timer >= 1.0:
                    fps = fps_counter / (current_time - fps_timer)
                    cv2.putText(frame_with_boxes, f"FPS: {fps:.1f}", (10, 30),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                    fps_counter = 0
                    fps_timer = current_time

                # 显示统计信息
                valid_rate = (stats['valid_frames'] / max(stats['frames_received'], 1)) * 100
                info_text = f"Valid: {valid_rate:.1f}% | Objects: {len(detections)}"
                cv2.putText(frame_with_boxes, info_text, (10, 60),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)

                # 更新显示帧数统计
                stats['frames_displayed'] += 1

                # 显示实时结果
                cv2.imshow(window_name, frame_with_boxes)
            except Empty:
                pass  # 结果队列为空是正常现象，无需处理
            except Exception as e:
                print(f"[ERROR] 结果处理异常: {e}")

        # 按键处理
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            print("[MAIN] 用户请求退出")
            break
        elif key == ord('s'):
            # 保存当前帧
            filename = f"rtsp_detection_{int(time())}.jpg"
            cv2.imwrite(filename, frame)
            print(f"[MAIN] 💾 保存截图: {filename}")

        # 每隔15秒打印一次统计信息
        if time() - last_stat_time > 15:
            print_stats()
            last_stat_time = time()

except KeyboardInterrupt:
    print("[MAIN] 程序被用户中断")
except Exception as e:
    print(f"[ERROR] 主循环异常: {e}")
    import traceback
    traceback.print_exc()
finally:
    print("[MAIN] 清理资源...")
    # 发送终止信号
    frame_queue.put(None)
    # 等待线程结束
    processing_thread.join(timeout=5.0)
    if processing_thread.is_alive():
        print("[WARNING] 处理线程未正常退出")
    else:
        print("[INFO] 处理线程已退出")

    # 打印最终统计
    print_stats()

    # 关闭窗口
    cv2.destroyAllWindows()
    print("[MAIN] 程序退出")
