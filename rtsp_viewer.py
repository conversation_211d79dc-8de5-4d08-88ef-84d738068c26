#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RTSP画面显示工具 - 输入主机号即可查看RTSP流
"""

import cv2
import argparse
import time
import sys

def display_rtsp_stream(host_number, port=8554, path="camera"):
    """
    显示RTSP视频流
    
    Args:
        host_number (str): 主机号（IP地址最后一段）
        port (int): RTSP端口号
        path (str): RTSP路径
    """
    # 构建RTSP URL
    rtsp_url = f"rtsp://192.168.0.{host_number}:{port}/{path}"
    print(f"[INFO] 正在连接RTSP流: {rtsp_url}")
    
    # 创建OpenCV视频捕获对象
    cap = cv2.VideoCapture(rtsp_url)
    
    # 设置缓冲区大小为1，减少延迟
    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
    
    # 检查连接是否成功
    if not cap.isOpened():
        print(f"[ERROR] 无法连接到RTSP流: {rtsp_url}")
        return False
    
    print(f"[SUCCESS] 成功连接到RTSP流")
    print(f"[INFO] 按 'q' 键退出，按 's' 键保存截图")
    
    # 创建窗口
    window_name = f"RTSP Stream - {rtsp_url}"
    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
    
    # 帧计数和FPS计算
    frame_count = 0
    start_time = time.time()
    fps = 0
    
    try:
        while True:
            # 读取一帧
            ret, frame = cap.read()
            
            # 检查是否成功读取
            if not ret:
                print("[WARNING] 无法读取帧，尝试重新连接...")
                # 重新连接
                cap.release()
                cap = cv2.VideoCapture(rtsp_url)
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                if not cap.isOpened():
                    print("[ERROR] 重新连接失败")
                    break
                continue
            
            # 计算FPS
            frame_count += 1
            elapsed_time = time.time() - start_time
            if elapsed_time >= 1.0:
                fps = frame_count / elapsed_time
                frame_count = 0
                start_time = time.time()
            
            # 在帧上显示信息
            cv2.putText(frame, f"FPS: {fps:.1f}", (10, 30), 
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, f"URL: {rtsp_url}", (10, 70), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # 显示帧
            cv2.imshow(window_name, frame)
            
            # 处理键盘输入
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print("[INFO] 用户退出")
                break
            elif key == ord('s'):
                # 保存截图
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"rtsp_capture_{timestamp}.jpg"
                cv2.imwrite(filename, frame)
                print(f"[INFO] 截图已保存: {filename}")
    
    except KeyboardInterrupt:
        print("[INFO] 用户中断")
    except Exception as e:
        print(f"[ERROR] 发生错误: {e}")
    finally:
        # 释放资源
        cap.release()
        cv2.destroyAllWindows()
        print("[INFO] 已关闭RTSP流")
    
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="RTSP画面显示工具")
    parser.add_argument("host_number", help="主机号（IP地址最后一段）")
    parser.add_argument("--port", type=int, default=8554, help="RTSP端口号")
    parser.add_argument("--path", default="camera", help="RTSP路径")
    
    # 如果没有参数，进入交互模式
    if len(sys.argv) == 1:
        host_number = input("请输入主机号（IP地址最后一段，如186）: ")
        port = input("请输入端口号（默认8554）: ") or "8554"
        path = input("请输入RTSP路径（默认camera）: ") or "camera"
        display_rtsp_stream(host_number, int(port), path)
    else:
        args = parser.parse_args()
        display_rtsp_stream(args.host_number, args.port, args.path)

if __name__ == "__main__":
    main()