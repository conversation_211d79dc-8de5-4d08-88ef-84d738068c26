#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的remote_control.py功能
"""

import sys
import os
sys.path.append('.')

from remote_control import RemoteController

def test_remote_control():
    """测试远程控制功能"""
    print("🧪 测试修改后的remote_control.py")
    print("=" * 50)
    
    # 测试初始化
    print("1️⃣ 测试初始化...")
    controller = RemoteController("142", 5001)
    print("✅ 初始化成功")
    
    # 测试连接
    print("\n2️⃣ 测试连接...")
    if controller.test_connection():
        print("✅ 连接测试成功")
        
        # 测试获取状态
        print("\n3️⃣ 测试获取状态...")
        status = controller.get_status()
        if status:
            print("✅ 状态获取成功")
        else:
            print("❌ 状态获取失败")
        
        # 测试启动检测
        print("\n4️⃣ 测试启动检测...")
        result = controller.start_detection()
        if result:
            print("✅ 启动检测成功")
        else:
            print("❌ 启动检测失败")
        
        # 等待一下
        import time
        print("\n⏳ 等待2秒...")
        time.sleep(2)
        
        # 再次获取状态
        print("\n5️⃣ 再次获取状态...")
        status = controller.get_status()
        
        # 测试停止检测
        print("\n6️⃣ 测试停止检测...")
        result = controller.stop_detection()
        if result:
            print("✅ 停止检测成功")
        else:
            print("❌ 停止检测失败")
    else:
        print("❌ 连接测试失败")
        print("   请确保v9.py正在运行在192.168.0.142:5001")
    
    print("\n🎉 测试完成")

if __name__ == "__main__":
    test_remote_control()
