#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
v8.py - RTSP垃圾检测系统 (Web控制界面版)
结合test_web_buttons.py和main_v7_all_.py的功能
- 左侧：系统信息 (CPU/GPU、模型等)
- 右侧：实时检测信息
- 按钮控制：启动/停止检测
- 无视频显示，专注于状态监控
"""

import os
import cv2
import numpy as np
import http.server
import socketserver
import json
import threading
import time
import socket
from threading import Lock
from queue import Queue, Empty
from ultralytics import YOLO
import torch

# 环境配置 - 抑制日志
os.environ['OPENCV_LOG_LEVEL'] = 'SILENT'
os.environ['OPENCV_VIDEOIO_DEBUG'] = '0'
os.environ['OPENCV_FFMPEG_LOGLEVEL'] = '-8'
os.environ['FFMPEG_HIDE_BANNER'] = '1'
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['YOLO_VERBOSE'] = 'False'

import warnings
warnings.filterwarnings('ignore')
cv2.setLogLevel(0)

# 配置类
class Config:
    # RTSP流配置
    RTSP_URL = "rtsp://192.168.0.186:8554/camera"
    RTSP_BUFFER_SIZE = 1
    
    # 检测配置
    CONFIDENCE_THRESHOLD = 0.5
    MODEL_PATH = 'yolov8n.pt'
    
    # HTTP配置
    HTTP_PORT = 5000
    
    # 性能配置
    QUEUE_SIZE = 3

# 全局变量
current_raw_frame = None
current_processed_frame = None
frame_lock = Lock()
frame_queue = Queue(maxsize=Config.QUEUE_SIZE)

# API控制变量
detection_enabled = False
processing_active = False

# 统计信息
stats = {
    'frames_received': 0,
    'frames_processed': 0,
    'valid_frames': 0,
    'error_frames': 0,
    'queue_drops': 0,
    'start_time': time.time()
}

# 系统信息
system_info = {
    'device': 'cpu',
    'model_loaded': False,
    'model_path': Config.MODEL_PATH,
    'rtsp_url': Config.RTSP_URL,
    'rtsp_connected': False,
    'python_version': '',
    'opencv_version': '',
    'torch_version': '',
    'local_ip': ''
}

# 设备检测和模型初始化
device = 'cuda' if torch.cuda.is_available() else 'cpu'
system_info['device'] = device
model = None

def get_local_ip():
    """获取本地IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def initialize_system():
    """初始化系统信息"""
    global system_info
    
    system_info['local_ip'] = get_local_ip()
    system_info['python_version'] = f"{torch.__version__}"
    system_info['opencv_version'] = cv2.__version__
    system_info['torch_version'] = torch.__version__
    
    print(f"[INIT] 系统初始化完成")
    print(f"[INIT] 设备: {device}")
    print(f"[INIT] 本地IP: {system_info['local_ip']}")
    print(f"[INIT] OpenCV版本: {system_info['opencv_version']}")
    print(f"[INIT] PyTorch版本: {system_info['torch_version']}")

def load_model():
    """延迟加载YOLO模型"""
    global model, system_info
    
    if model is None:
        try:
            print(f"[MODEL] 加载YOLO模型: {Config.MODEL_PATH}")
            model = YOLO(Config.MODEL_PATH)
            if device == 'cuda':
                model.to(device)
            system_info['model_loaded'] = True
            print(f"[MODEL] 模型加载成功，设备: {device}")
            return True
        except Exception as e:
            print(f"[ERROR] 模型加载失败: {e}")
            system_info['model_loaded'] = False
            return False
    return True

def is_frame_valid(frame):
    """检查帧是否有效"""
    if frame is None or frame.size == 0:
        return False
    if len(frame.shape) < 2:
        return False
    return True

def process_frames():
    """帧处理线程"""
    global current_processed_frame, detection_enabled, processing_active
    
    print("[THREAD] 帧处理线程启动")
    
    while True:
        try:
            # 获取待处理帧
            frame = frame_queue.get(timeout=2)
            
            if is_frame_valid(frame):
                stats['valid_frames'] += 1
                
                # 只有在检测启用时才进行YOLO处理
                if detection_enabled:
                    # 确保模型已加载
                    if not load_model():
                        processed_frame = frame.copy()
                        cv2.putText(processed_frame, "Model Loading Failed", (10, 30), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                    else:
                        # YOLO检测
                        results = model(frame, imgsz=416, conf=Config.CONFIDENCE_THRESHOLD, 
                                      verbose=False, device=device)[0]
                        
                        processed_frame = frame.copy()
                        detections = 0
                        
                        # 解析检测结果
                        if hasattr(results, 'boxes') and results.boxes is not None:
                            detections = len(results.boxes)
                        
                        # 添加检测信息
                        cv2.putText(processed_frame, f"Objects: {detections} | Device: {device}",
                                    (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                        
                        stats['frames_processed'] += 1
                        
                        # 每50帧打印一次
                        if stats['frames_processed'] % 50 == 0:
                            print(f"[PROCESS] 已处理 {stats['frames_processed']} 帧")
                else:
                    # 检测禁用
                    processed_frame = frame.copy()
                    cv2.putText(processed_frame, "Detection DISABLED",
                                (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                
                # 更新全局处理帧
                with frame_lock:
                    current_processed_frame = processed_frame
            else:
                stats['error_frames'] += 1
                
        except Empty:
            continue
        except Exception as e:
            print(f"[ERROR] 帧处理错误: {e}")
            stats['error_frames'] += 1

class WebHandler(http.server.BaseHTTPRequestHandler):
    """Web界面处理器"""
    
    def do_GET(self):
        if self.path == '/':
            self.send_main_page()
        elif self.path == '/video_feed':
            self.send_video_feed()
        else:
            self.send_error(404)
    
    def do_POST(self):
        global detection_enabled, processing_active
        
        if self.path == '/api/start':
            detection_enabled = True
            processing_active = True
            response = {"status": "success", "message": "Detection started"}
            print(f"[API] ✅ 检测启动 - detection_enabled: {detection_enabled}")
        elif self.path == '/api/stop':
            detection_enabled = False
            processing_active = False
            response = {"status": "success", "message": "Detection stopped"}
            print(f"[API] 🛑 检测停止 - detection_enabled: {detection_enabled}")
        elif self.path == '/api/status':
            # 计算运行时间和FPS
            elapsed = time.time() - stats['start_time']
            fps_received = stats['frames_received'] / elapsed if elapsed > 0 else 0
            fps_processed = stats['frames_processed'] / elapsed if elapsed > 0 else 0
            
            response = {
                "status": "success",
                "detection_enabled": detection_enabled,
                "processing_active": processing_active,
                "stats": {
                    **stats,
                    "elapsed_time": elapsed,
                    "fps_received": fps_received,
                    "fps_processed": fps_processed
                },
                "system_info": system_info
            }
        else:
            response = {"status": "error", "message": "Unknown API endpoint"}
        
        # 发送JSON响应
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())
    
    def send_main_page(self):
        """发送主页"""
        local_ip = get_local_ip()
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>RTSP垃圾检测控制台</title>
            <style>
                body {{ font-family: Arial, sans-serif; background: #f0f2f5; color: #333; margin: 0; padding: 20px; }}
                .container {{ max-width: 1200px; margin: 0 auto; }}
                h1 {{ text-align: center; color: #2c3e50; margin-bottom: 30px; }}
                .main-content {{ display: flex; gap: 20px; }}
                .left-panel, .right-panel {{ flex: 1; background: #ffffff; padding: 20px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); }}
                .panel-title {{ font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 5px; }}
                .info-item {{ margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 5px; }}
                .info-label {{ font-weight: bold; color: #555; }}
                .info-value {{ color: #333; }}
                .controls {{ text-align: center; margin: 30px 0; }}
                .status {{ padding: 15px; margin: 20px 0; border-radius: 8px; font-weight: bold; text-align: center; }}
                .btn {{ padding: 12px 24px; margin: 8px; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: bold; transition: all 0.3s; }}
                .btn-start {{ background: #28a745; color: white; }}
                .btn-start:hover {{ background: #218838; }}
                .btn-stop {{ background: #dc3545; color: white; }}
                .btn-stop:hover {{ background: #c82333; }}
                .btn-refresh {{ background: #007bff; color: white; }}
                .btn-refresh:hover {{ background: #0056b3; }}
                .stats-grid {{ display: grid; grid-template-columns: 1fr 1fr; gap: 10px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🗑️ RTSP垃圾检测控制台</h1>
                
                <div class="status" id="status">检测状态: 加载中...</div>
                
                <div class="controls">
                    <button class="btn btn-start" onclick="startDetection()">🚀 启动检测</button>
                    <button class="btn btn-stop" onclick="stopDetection()">🛑 停止检测</button>
                    <button class="btn btn-refresh" onclick="updateStatus()">🔄 刷新状态</button>
                </div>
                
                <div class="main-content">
                    <div class="left-panel">
                        <div class="panel-title">📊 系统信息</div>
                        <div id="system-info">
                            <div class="info-item">
                                <span class="info-label">本地IP:</span>
                                <span class="info-value" id="local-ip">{local_ip}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">计算设备:</span>
                                <span class="info-value" id="device">检测中...</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">模型状态:</span>
                                <span class="info-value" id="model-status">检测中...</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">RTSP源:</span>
                                <span class="info-value" id="rtsp-url">检测中...</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">OpenCV版本:</span>
                                <span class="info-value" id="opencv-version">检测中...</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">PyTorch版本:</span>
                                <span class="info-value" id="torch-version">检测中...</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="right-panel">
                        <div class="panel-title">📈 实时检测信息</div>
                        <div id="detection-info">
                            <div class="stats-grid">
                                <div class="info-item">
                                    <span class="info-label">运行时间:</span>
                                    <span class="info-value" id="elapsed-time">0秒</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">接收帧数:</span>
                                    <span class="info-value" id="frames-received">0</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">处理帧数:</span>
                                    <span class="info-value" id="frames-processed">0</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">有效帧数:</span>
                                    <span class="info-value" id="valid-frames">0</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">接收FPS:</span>
                                    <span class="info-value" id="fps-received">0.0</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">处理FPS:</span>
                                    <span class="info-value" id="fps-processed">0.0</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">错误帧数:</span>
                                    <span class="info-value" id="error-frames">0</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">队列丢弃:</span>
                                    <span class="info-value" id="queue-drops">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <script>
                function updateStatus() {{
                    fetch('/api/status', {{method: 'POST'}})
                        .then(response => response.json())
                        .then(data => {{
                            // 更新检测状态
                            const statusDiv = document.getElementById('status');
                            if (data.detection_enabled) {{
                                statusDiv.style.background = '#28a745';
                                statusDiv.style.color = 'white';
                                statusDiv.innerHTML = '检测状态: <strong>🟢 启用中</strong>';
                            }} else {{
                                statusDiv.style.background = '#dc3545';
                                statusDiv.style.color = 'white';
                                statusDiv.innerHTML = '检测状态: <strong>🔴 禁用</strong>';
                            }}
                            
                            // 更新系统信息
                            const sysInfo = data.system_info;
                            document.getElementById('device').textContent = sysInfo.device.toUpperCase() + (sysInfo.device === 'cuda' ? ' (GPU加速)' : ' (CPU模式)');
                            document.getElementById('model-status').textContent = sysInfo.model_loaded ? '✅ 已加载' : '❌ 未加载';
                            document.getElementById('rtsp-url').textContent = sysInfo.rtsp_url;
                            document.getElementById('opencv-version').textContent = sysInfo.opencv_version;
                            document.getElementById('torch-version').textContent = sysInfo.torch_version;
                            
                            // 更新检测信息
                            const stats = data.stats;
                            document.getElementById('elapsed-time').textContent = Math.floor(stats.elapsed_time) + '秒';
                            document.getElementById('frames-received').textContent = stats.frames_received;
                            document.getElementById('frames-processed').textContent = stats.frames_processed;
                            document.getElementById('valid-frames').textContent = stats.valid_frames;
                            document.getElementById('fps-received').textContent = stats.fps_received.toFixed(1);
                            document.getElementById('fps-processed').textContent = stats.fps_processed.toFixed(1);
                            document.getElementById('error-frames').textContent = stats.error_frames;
                            document.getElementById('queue-drops').textContent = stats.queue_drops;
                            
                            console.log('状态更新完成');
                        }})
                        .catch(error => {{
                            console.error('状态更新失败:', error);
                            document.getElementById('status').innerHTML = '检测状态: <strong>❌ 更新失败</strong>';
                        }});
                }}
                
                function startDetection() {{
                    fetch('/api/start', {{method: 'POST'}})
                        .then(response => response.json())
                        .then(data => {{
                            alert('✅ ' + data.message);
                            updateStatus();
                        }})
                        .catch(error => {{
                            alert('❌ 启动失败: ' + error);
                        }});
                }}
                
                function stopDetection() {{
                    fetch('/api/stop', {{method: 'POST'}})
                        .then(response => response.json())
                        .then(data => {{
                            alert('✅ ' + data.message);
                            updateStatus();
                        }})
                        .catch(error => {{
                            alert('❌ 停止失败: ' + error);
                        }});
                }}
                
                // 页面加载时立即更新状态
                document.addEventListener('DOMContentLoaded', function() {{
                    updateStatus();
                    // 每3秒自动更新状态
                    setInterval(updateStatus, 3000);
                }});
            </script>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode())
    
    def send_video_feed(self):
        """发送视频流"""
        self.send_response(200)
        self.send_header('Content-Type', 'multipart/x-mixed-replace; boundary=frame')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()

        try:
            while True:
                frame_to_send = None

                # 获取当前处理帧
                with frame_lock:
                    if current_processed_frame is not None:
                        frame_to_send = current_processed_frame.copy()
                    elif current_raw_frame is not None:
                        frame_to_send = current_raw_frame.copy()

                if frame_to_send is not None:
                    # 编码为JPEG
                    ret, jpeg = cv2.imencode('.jpg', frame_to_send, [cv2.IMWRITE_JPEG_QUALITY, 85])

                    if ret:
                        # 发送MJPEG帧
                        self.wfile.write(b'--frame\r\n')
                        self.wfile.write(b'Content-Type: image/jpeg\r\n\r\n')
                        self.wfile.write(jpeg.tobytes())
                        self.wfile.write(b'\r\n')
                else:
                    # 发送黑色帧
                    black_frame = np.zeros((480, 640, 3), dtype=np.uint8)
                    cv2.putText(black_frame, "No Video Signal", (200, 240),
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

                    ret, jpeg = cv2.imencode('.jpg', black_frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                    if ret:
                        self.wfile.write(b'--frame\r\n')
                        self.wfile.write(b'Content-Type: image/jpeg\r\n\r\n')
                        self.wfile.write(jpeg.tobytes())
                        self.wfile.write(b'\r\n')

                time.sleep(1.0 / 15)  # 15 FPS

        except Exception as e:
            print(f"[ERROR] 视频流发送错误: {e}")

    def log_message(self, fmt, *args):
        # 抑制HTTP日志
        pass

def rtsp_capture_thread():
    """RTSP流获取线程"""
    global current_raw_frame, processing_active, system_info

    cap = None
    reconnect_count = 0
    max_reconnects = 3

    print("[THREAD] RTSP获取线程启动")

    while True:
        try:
            # 尝试连接RTSP流
            if cap is None or not cap.isOpened():
                print(f"[RTSP] 尝试连接: {Config.RTSP_URL}")

                # 设置日志抑制
                cv2.setLogLevel(0)
                os.environ['OPENCV_FFMPEG_LOGLEVEL'] = '-8'

                cap = cv2.VideoCapture(Config.RTSP_URL)
                cap.set(cv2.CAP_PROP_BUFFERSIZE, Config.RTSP_BUFFER_SIZE)

                if not cap.isOpened():
                    reconnect_count += 1
                    print(f"[WARNING] RTSP连接失败，重试中... ({reconnect_count}/{max_reconnects})")
                    system_info['rtsp_connected'] = False

                    if reconnect_count >= max_reconnects:
                        print("[INFO] RTSP连接失败，使用测试模式")
                        # 使用测试帧模式
                        while True:
                            test_frame = create_test_frame()

                            with frame_lock:
                                current_raw_frame = test_frame.copy()

                            # 放入处理队列
                            try:
                                frame_queue.put_nowait(test_frame.copy())
                            except:
                                # 队列满，清空并放入新帧
                                try:
                                    while not frame_queue.empty():
                                        frame_queue.get_nowait()
                                        stats['queue_drops'] += 1
                                    frame_queue.put_nowait(test_frame.copy())
                                except:
                                    pass

                            stats['frames_received'] += 1
                            time.sleep(1.0 / 10)  # 10 FPS测试模式

                    time.sleep(2)
                    continue
                else:
                    print("[RTSP] 连接成功")
                    system_info['rtsp_connected'] = True
                    reconnect_count = 0

            # 读取帧
            ret, frame = cap.read()

            if not ret:
                print("[WARNING] 无法读取RTSP帧，尝试重连...")
                cap.release()
                cap = None
                system_info['rtsp_connected'] = False
                continue

            # 更新统计
            stats['frames_received'] += 1

            # 更新原始帧
            with frame_lock:
                current_raw_frame = frame.copy()

            # 放入处理队列
            try:
                frame_queue.put_nowait(frame.copy())
            except:
                # 队列满，清空并放入新帧
                try:
                    while not frame_queue.empty():
                        frame_queue.get_nowait()
                        stats['queue_drops'] += 1
                    frame_queue.put_nowait(frame.copy())
                except:
                    pass

            # 控制帧率
            time.sleep(1.0 / 30)  # 30 FPS

        except Exception as e:
            print(f"[ERROR] RTSP流错误: {e}")
            if cap:
                cap.release()
                cap = None
            system_info['rtsp_connected'] = False
            time.sleep(2)

def create_test_frame():
    """创建测试帧"""
    frame = np.full((480, 640, 3), 64, dtype=np.uint8)

    # 添加测试信息
    cv2.rectangle(frame, (0, 0), (640, 80), (50, 50, 200), -1)
    cv2.putText(frame, "RTSP Test Mode", (200, 30),
                cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 255, 255), 2)
    cv2.putText(frame, f"Time: {time.strftime('%H:%M:%S')}", (200, 60),
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

    return frame

def print_stats():
    """打印统计信息"""
    while True:
        time.sleep(30)  # 每30秒打印一次

        elapsed = time.time() - stats['start_time']
        fps_received = stats['frames_received'] / elapsed if elapsed > 0 else 0
        fps_processed = stats['frames_processed'] / elapsed if elapsed > 0 else 0

        print("\n" + "=" * 60)
        print("📊 RTSP垃圾检测系统统计")
        print("=" * 60)
        print(f"运行时间: {elapsed:.1f}秒 | 设备: {device}")
        print(f"检测状态: {'🟢 启用' if detection_enabled else '🔴 禁用'}")
        print(f"RTSP连接: {'🟢 正常' if system_info['rtsp_connected'] else '🔴 断开'}")
        print(f"模型状态: {'🟢 已加载' if system_info['model_loaded'] else '🔴 未加载'}")
        print(f"帧统计: 接收{stats['frames_received']}({fps_received:.1f}FPS) 处理{stats['frames_processed']}({fps_processed:.1f}FPS)")
        print(f"有效帧: {stats['valid_frames']} | 错误帧: {stats['error_frames']} | 队列丢弃: {stats['queue_drops']}")
        print("=" * 60 + "\n")

def main():
    """主函数"""
    global current_raw_frame, detection_enabled, processing_active

    print("🚀 启动RTSP垃圾检测控制台")
    print("=" * 50)

    # 初始化系统
    initialize_system()

    local_ip = get_local_ip()
    print(f"[MAIN] Web控制台: http://{local_ip}:{Config.HTTP_PORT}")
    print(f"[MAIN] Web端视频: http://{local_ip}:{Config.HTTP_PORT}/video_feed")
    print(f"[MAIN] API控制:")
    print(f"    - 启动检测: POST http://{local_ip}:{Config.HTTP_PORT}/api/start")
    print(f"    - 停止检测: POST http://{local_ip}:{Config.HTTP_PORT}/api/stop")
    print(f"    - 获取状态: POST http://{local_ip}:{Config.HTTP_PORT}/api/status")

    # 启动帧处理线程
    print("[MAIN] 启动帧处理线程...")
    processing_thread = threading.Thread(target=process_frames, daemon=True)
    processing_thread.start()

    # 启动RTSP获取线程
    print("[MAIN] 启动RTSP获取线程...")
    rtsp_thread = threading.Thread(target=rtsp_capture_thread, daemon=True)
    rtsp_thread.start()

    # 启动统计线程
    print("[MAIN] 启动统计线程...")
    stats_thread = threading.Thread(target=print_stats, daemon=True)
    stats_thread.start()

    # 启动HTTP服务器
    print(f"[MAIN] 启动Web服务器在端口 {Config.HTTP_PORT}...")
    try:
        with socketserver.TCPServer(("", Config.HTTP_PORT), WebHandler) as httpd:
            print("✅ 系统启动完成！")
            print("💡 打开浏览器访问Web控制台")
            httpd.serve_forever()
    except Exception as e:
        print(f"❌ Web服务器启动失败: {e}")

if __name__ == "__main__":
    main()
