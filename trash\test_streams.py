#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试流地址连通性
"""

import cv2
import requests
import time

def test_http_stream(url):
    """测试HTTP流"""
    print(f"🌐 测试HTTP流: {url}")
    try:
        response = requests.get(url, timeout=10, stream=True)
        print(f"  状态码: {response.status_code}")
        print(f"  内容类型: {response.headers.get('content-type', 'Unknown')}")
        print(f"  内容长度: {response.headers.get('content-length', 'Unknown')}")
        
        if response.status_code == 200:
            # 尝试读取一些数据
            data = response.raw.read(1024)
            print(f"  数据样本: {len(data)} bytes")
            print("  ✅ HTTP流可访问")
            return True
        else:
            print("  ❌ HTTP流访问失败")
            return False
            
    except Exception as e:
        print(f"  ❌ HTTP流错误: {e}")
        return False

def test_rtsp_stream(url):
    """测试RTSP流"""
    print(f"📹 测试RTSP流: {url}")
    try:
        cap = cv2.VideoCapture(url)
        
        if not cap.isOpened():
            print("  ❌ RTSP流无法打开")
            return False
        
        # 尝试读取一帧
        ret, frame = cap.read()
        
        if ret and frame is not None:
            print(f"  帧尺寸: {frame.shape}")
            print("  ✅ RTSP流可访问")
            cap.release()
            return True
        else:
            print("  ❌ RTSP流无法读取帧")
            cap.release()
            return False
            
    except Exception as e:
        print(f"  ❌ RTSP流错误: {e}")
        return False

def main():
    print("🔍 流地址连通性测试")
    print("=" * 50)
    
    # 测试地址
    http_url = "http://192.168.0.186:8889/camera/"
    rtsp_url = "rtsp://192.168.0.186:8554/camera"
    
    # 测试HTTP流
    http_ok = test_http_stream(http_url)
    print()
    
    # 测试RTSP流
    rtsp_ok = test_rtsp_stream(rtsp_url)
    print()
    
    # 总结
    print("📊 测试结果:")
    print(f"  HTTP流 ({http_url}): {'✅ 可用' if http_ok else '❌ 不可用'}")
    print(f"  RTSP流 ({rtsp_url}): {'✅ 可用' if rtsp_ok else '❌ 不可用'}")
    
    if rtsp_ok:
        print("\n💡 建议使用RTSP流，性能更好")
    elif http_ok:
        print("\n💡 可以使用HTTP流作为备选")
    else:
        print("\n⚠️ 两个流都不可用，请检查网络和设备")

if __name__ == "__main__":
    main()
