#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RTSP流垃圾检测 + HTTP推流
结合 i_local.py 和 new_main.py 的功能
- 从RTSP流获取视频
- YOLOv8垃圾检测 (GPU加速)
- HTTP MJPEG流推送
"""

import cv2
import torch
import numpy as np
from ultralytics import YOLO
from threading import Thread, Lock
from queue import Queue, Empty
from time import time, sleep
import os
import warnings
import socket
import http.server

# 环境配置抑制日志
os.environ['OPENCV_LOG_LEVEL'] = 'SILENT'
os.environ['OPENCV_VIDEOIO_DEBUG'] = '0'
os.environ['FFMPEG_LOG_LEVEL'] = 'quiet'
os.environ['AV_LOG_FORCE_NOCOLOR'] = '1'
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

# 保留stderr用于重要错误信息
# H.264解码错误通过环境变量抑制

warnings.filterwarnings('ignore')
cv2.setLogLevel(0)

# 设备检测和配置
device = 'cuda' if torch.cuda.is_available() else 'cpu'

# 打印环境信息
print("=" * 50)
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA device count: {torch.cuda.device_count()}")
    print(f"CUDA device name: {torch.cuda.get_device_name(0)}")
    print(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
print(f"Using device: {device}")
print(f"OpenCV version: {cv2.__version__}")
print("=" * 50)

# 配置类
class Config:
    # RTSP配置
    RTSP_URL = "rtsp://*************:8554/camera"
    RTSP_RECONNECT_MAX = 5
    RTSP_BUFFER_SIZE = 1

    # 检测配置
    CONFIDENCE_THRESHOLD = 0.5
    GARBAGE_CLASSES = {39: 'bottle', 67: 'cell phone'}
    MODEL_PATH = 'yolov8n.pt'

    # 性能配置
    QUEUE_SIZE = 3  # 减少队列大小，降低延迟
    STATS_INTERVAL = 30  # 统计信息打印间隔（秒）
    HTTP_FPS = 20  # HTTP流帧率

    # HTTP配置
    HTTP_PORT = 5000
    ENABLE_DISPLAY = False  # 是否启用OpenCV窗口显示

# 向后兼容
garbage_classes = Config.GARBAGE_CLASSES
CONFIDENCE_THRESHOLD = Config.CONFIDENCE_THRESHOLD
QUEUE_SIZE = Config.QUEUE_SIZE
STATS_INTERVAL = Config.STATS_INTERVAL

# 模型初始化
model = YOLO(Config.MODEL_PATH)

# 将模型移动到指定设备
if device == 'cuda':
    model.to(device)
    print(f"[DEBUG] 模型已加载到GPU: {device}")
else:
    print(f"[DEBUG] 模型已加载到CPU: {device}")

print("[DEBUG] 模型加载成功")

# 全局变量
frame_queue = Queue(maxsize=QUEUE_SIZE)
current_frame = None
frame_lock = Lock()

# 统计信息
stats = {
    'frames_received': 0,
    'frames_processed': 0, 
    'frames_displayed': 0,
    'valid_frames': 0,
    'error_frames': 0, 
    'queue_drops': 0, 
    'start_time': time()
}

def get_local_ip():
    """获取本机局域网IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.settimeout(2)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception as e:
        print(f"[WARNING] 获取IP失败: {e}, 使用默认IP")
        return "*************"

class StreamingHandler(http.server.BaseHTTPRequestHandler):
    """HTTP流媒体处理器"""

    def do_GET(self):
        global current_frame
        
        if self.path == '/':
            # 主页面
            self.send_response(200)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.end_headers()
            
            local_ip = get_local_ip()
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>RTSP Garbage Detection Stream</title>
                <style>
                    body {{ font-family: Arial; text-align: center; background: #f0f0f0; }}
                    .container {{ max-width: 1000px; margin: 0 auto; padding: 20px; }}
                    .info {{ background: #fff; padding: 15px; margin: 10px 0; border-radius: 8px; }}
                    img {{ border: 2px solid #333; max-width: 100%; }}
                    h1 {{ color: #333; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🗑️ RTSP Garbage Detection Stream</h1>
                    <div class="info">
                        <p>Real-time Detection: bottle and cell phone</p>
                        <p>RTSP Source: rtsp://*************:8554/camera</p>
                        <p>Access URL: http://{local_ip}:5000</p>
                    </div>
                    <img src="/video_feed" alt="Live Detection Stream">
                    <div class="info">
                        <p>💡 Tip: Page will auto-refresh to show latest detection results</p>
                    </div>
                </div>
            </body>
            </html>
            """
            self.wfile.write(html.encode())
            
        elif self.path == '/video_feed':
            # MJPEG流
            self.send_response(200)
            self.send_header('Content-Type', 'multipart/x-mixed-replace; boundary=frame')
            self.send_header('Cache-Control', 'no-cache')
            self.send_header('Connection', 'close')
            self.end_headers()
            
            frame_count = 0
            while True:
                try:
                    frame_to_send = None
                    with frame_lock:
                        if current_frame is not None:
                            frame_to_send = current_frame.copy()
                    
                    # 如果没有帧，创建一个默认帧
                    if frame_to_send is None:
                        frame_to_send = create_test_frame()
                        cv2.putText(frame_to_send, f"HTTP Frame: {frame_count}", (20, 60), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 2)
                    
                    # 编码为JPEG
                    ret, buffer = cv2.imencode('.jpg', frame_to_send, 
                                             [cv2.IMWRITE_JPEG_QUALITY, 85])
                    if ret:
                        frame_bytes = buffer.tobytes()
                        
                        # 发送MJPEG帧
                        self.wfile.write(b'--frame\r\n')
                        self.wfile.write(b'Content-Type: image/jpeg\r\n')
                        self.wfile.write(f'Content-Length: {len(frame_bytes)}\r\n\r\n'.encode())
                        self.wfile.write(frame_bytes)
                        self.wfile.write(b'\r\n')
                        self.wfile.flush()
                        
                        frame_count += 1
                    
                    sleep(1.0 / Config.HTTP_FPS)  # 根据配置控制帧率

                except ConnectionResetError:
                    # 客户端断开连接，正常情况
                    break
                except BrokenPipeError:
                    # 管道断开，正常情况
                    break
                except Exception as e:
                    # 其他错误才打印
                    if "connection" not in str(e).lower():
                        print(f"[HTTP] 流传输错误: {e}")
                    break
        else:
            self.send_response(404)
            self.end_headers()

    def log_message(self, format, *args):
        # 抑制HTTP服务器日志
        _ = format, args
        pass

def create_test_frame():
    """创建测试帧"""
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 添加文字
    cv2.putText(frame, "RTSP Stream Not Available", (150, 200), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    cv2.putText(frame, "Showing Test Pattern", (200, 250), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
    cv2.putText(frame, f"Time: {int(time())}", (250, 300), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    # 添加图形
    cv2.circle(frame, (320, 350), 50, (0, 0, 255), 3)
    cv2.rectangle(frame, (250, 400), (390, 450), (255, 0, 0), 3)
    
    return frame

def is_frame_valid(frame):
    """检查帧是否有效"""
    if frame is None or frame.size == 0: 
        return False
    if len(frame.shape) != 3 or frame.shape[2] != 3: 
        return False
    if frame.shape[0] < 100 or frame.shape[1] < 100: 
        return False
    mean_val = np.mean(frame)
    if mean_val < 10 or mean_val > 245: 
        return False
    return True

def draw_detections(frame, detections):
    """在帧上绘制检测结果"""
    for x1, y1, x2, y2, label in detections:
        # 绘制边框
        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        
        # 绘制标签背景
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
        cv2.rectangle(frame, (x1, y1 - label_size[1] - 10), 
                     (x1 + label_size[0], y1), (0, 255, 0), -1)
        
        # 绘制标签文字
        cv2.putText(frame, label, (x1, y1 - 5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    
    return frame

def process_frames():
    """处理帧的线程函数"""
    global current_frame

    while True:
        try:
            # 从队列获取帧
            frame = frame_queue.get(timeout=5)

            if not is_frame_valid(frame):
                stats['error_frames'] += 1
                continue

            stats['valid_frames'] += 1

            # YOLO检测 (使用配置的设备)
            results = model(frame, imgsz=416, conf=CONFIDENCE_THRESHOLD, verbose=False, device=device)[0]
            detections = []

            for box in results.boxes:
                class_id = int(box.cls)
                if class_id in garbage_classes:
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    confidence = float(box.conf)
                    label = f"{garbage_classes[class_id]} {confidence:.2f}"
                    detections.append((x1, y1, x2, y2, label))

            # 绘制检测结果
            processed_frame = draw_detections(frame.copy(), detections)

            # 添加信息文字
            cv2.putText(processed_frame, f"Objects: {len(detections)} | Device: {device}",
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            cv2.putText(processed_frame, f"IP: {get_local_ip()}",
                       (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

            # 更新全局帧
            with frame_lock:
                current_frame = processed_frame

            stats['frames_processed'] += 1

        except Empty:
            print("[WARNING] 帧处理线程等待超时")
            continue
        except Exception as e:
            print(f"[PROCESS_THREAD] 处理线程异常: {e}")
            break

    print("[PROCESS_THREAD] 处理线程退出")

def start_http_server(port=5000):
    """启动HTTP流媒体服务器"""
    try:
        from http.server import HTTPServer
        # 绑定到所有接口，确保局域网可访问
        httpd = HTTPServer(("0.0.0.0", port), StreamingHandler)
        local_ip = get_local_ip()
        print(f"[HTTP] HTTP流媒体服务器启动")
        print(f"[HTTP] 本地访问: http://localhost:{port}")
        print(f"[HTTP] 局域网访问: http://{local_ip}:{port}")
        print(f"[HTTP] 直接视频流: http://{local_ip}:{port}/video_feed")

        # 设置服务器超时，避免无限阻塞
        httpd.timeout = 1.0

        # 启动服务器循环
        print("[HTTP] 服务器开始监听...")
        while True:
            try:
                httpd.handle_request()
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"[HTTP] 请求处理错误: {e}")
                continue

    except Exception as e:
        print(f"[HTTP] 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()

def print_stats():
    """打印统计信息"""
    elapsed = time() - stats['start_time']
    fps_received = stats['frames_received'] / elapsed if elapsed > 0 else 0
    fps_processed = stats['frames_processed'] / elapsed if elapsed > 0 else 0
    fps_displayed = stats['frames_displayed'] / elapsed if elapsed > 0 else 0
    valid_rate = (stats['valid_frames'] / max(stats['frames_received'], 1)) * 100

    print("\n" + "=" * 60)
    print("📊 运行统计")
    print("=" * 60)
    print(f"运行时间: {elapsed:.1f}秒")
    print(f"设备: {device}")

    # GPU信息
    if device == 'cuda' and torch.cuda.is_available():
        gpu_memory_used = torch.cuda.memory_allocated(0) / 1024**3
        gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / 1024**3
        gpu_utilization = (gpu_memory_used / gpu_memory_total) * 100
        print(f"GPU内存: {gpu_memory_used:.2f}GB / {gpu_memory_total:.1f}GB ({gpu_utilization:.1f}%)")

    print(f"收到帧数: {stats['frames_received']} ({fps_received:.1f} FPS)")
    print(f"有效帧数: {stats['valid_frames']} (有效率: {valid_rate:.1f}%)")
    print(f"处理帧数: {stats['frames_processed']} ({fps_processed:.1f} FPS)")
    print(f"显示帧数: {stats['frames_displayed']} ({fps_displayed:.1f} FPS)")
    print(f"队列丢弃: {stats['queue_drops']}")
    print("=" * 60 + "\n")

def main():
    """主函数"""
    global current_frame

    # 获取本机IP
    local_ip = get_local_ip()
    print(f"[INFO] 本机IP地址: {local_ip}")

    # 启动处理线程
    Thread(target=process_frames, daemon=True).start()
    print("[THREAD] 帧处理线程启动")

    # 启动HTTP流媒体服务器（后台线程）
    http_port = Config.HTTP_PORT
    http_thread = Thread(target=start_http_server, args=(http_port,))
    http_thread.daemon = True  # 设为daemon，随主程序退出
    http_thread.start()
    sleep(2)  # 等待服务器启动

    # RTSP流配置
    stream_url = Config.RTSP_URL

    print(f"[MAIN] 启动RTSP流垃圾检测")
    print(f"[MAIN] RTSP源: {stream_url}")
    print(f"[MAIN] 🚀 运行设备: {device}")
    if device == 'cuda':
        print(f"[MAIN] 🎮 GPU加速: 已启用")
    else:
        print(f"[MAIN] 💻 CPU模式: 已启用")
    print(f"[MAIN] 💡 按 'q' 退出，按 's' 保存截图")
    print(f"[MAIN] 🌐 HTTP流媒体: http://{local_ip}:{http_port}")
    print(f"[MAIN] 🌐 局域网访问: 在浏览器打开上述地址")

    # 主循环变量
    fps_counter = 0
    fps_timer = time()
    last_stats_time = time()

    try:
        # 打开RTSP流
        print(f"[RTSP] 尝试连接RTSP流: {stream_url}")
        cap = cv2.VideoCapture(stream_url)

        if not cap.isOpened():
            print(f"[ERROR] 无法打开RTSP流: {stream_url}")
            print("[INFO] 将使用测试模式运行HTTP服务器")

            # 即使RTSP失败，也保持HTTP服务器运行
            try:
                while True:
                    sleep(1)
                    # 定期打印统计信息
                    if time() - last_stats_time > 20:
                        print_stats()
                        last_stats_time = time()
            except KeyboardInterrupt:
                print("\n[MAIN] 收到退出信号")
            return

        print(f"[RTSP] RTSP流连接成功")

        # 优化RTSP设置
        cap.set(cv2.CAP_PROP_BUFFERSIZE, Config.RTSP_BUFFER_SIZE)  # 最小缓冲，减少延迟
        cap.set(cv2.CAP_PROP_FPS, 30)  # 设置期望帧率

        # 连接重试计数
        reconnect_count = 0
        max_reconnects = Config.RTSP_RECONNECT_MAX

        # 主循环
        while True:
            ret, frame = cap.read()

            if not ret:
                reconnect_count += 1
                print(f"[WARNING] 无法读取RTSP帧，尝试重连... ({reconnect_count}/{max_reconnects})")

                cap.release()
                sleep(min(reconnect_count * 2, 10))  # 递增延迟，最大10秒

                cap = cv2.VideoCapture(stream_url)
                if not cap.isOpened():
                    if reconnect_count >= max_reconnects:
                        print(f"[ERROR] RTSP重连失败，已达到最大重试次数 ({max_reconnects})")
                        break
                    continue
                else:
                    print("[INFO] RTSP重连成功")
                    reconnect_count = 0  # 重置计数
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, Config.RTSP_BUFFER_SIZE)
                continue

            # 重置重连计数（成功读取帧）
            if reconnect_count > 0:
                reconnect_count = 0

            stats['frames_received'] += 1

            # 智能队列管理
            try:
                frame_queue.put_nowait(frame)
            except:
                # 队列满时，清空队列并放入新帧（保持最新帧）
                try:
                    while not frame_queue.empty():
                        frame_queue.get_nowait()
                        stats['queue_drops'] += 1
                    frame_queue.put_nowait(frame)
                except Exception as e:
                    print(f"[WARNING] 队列管理错误: {e}")

            # 可选的窗口显示（根据配置）
            try:
                if Config.ENABLE_DISPLAY and current_frame is not None:
                    display_frame = current_frame.copy()
                    cv2.imshow("RTSP Garbage Detection", display_frame)
                    stats['frames_displayed'] += 1

                    # 处理按键
                    key = cv2.waitKey(1) & 0xFF
                    if key == ord('q'):
                        print("[MAIN] 用户请求退出")
                        break
                    elif key == ord('s'):
                        if current_frame is not None:
                            filename = f"screenshot_{int(time())}.jpg"
                            cv2.imwrite(filename, current_frame)
                            print(f"[MAIN] 截图已保存: {filename}")
                else:
                    # 无显示器环境，使用键盘中断退出
                    sleep(0.01)  # 减少CPU占用
            except Exception as e:
                # 显示相关错误不影响主功能
                if "cannot connect to X server" not in str(e).lower():
                    print(f"[WARNING] 显示错误: {e}")
                sleep(0.01)

            # FPS计算和统计
            fps_counter += 1
            if time() - fps_timer > 1.0:
                fps_timer = time()
                fps_counter = 0

            # 定期打印统计信息
            if time() - last_stats_time > STATS_INTERVAL:
                print_stats()
                last_stats_time = time()

    except KeyboardInterrupt:
        print("\n[MAIN] 收到退出信号")
    except Exception as e:
        print(f"[ERROR] 主循环异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        try:
            cap.release()
        except:
            pass
        cv2.destroyAllWindows()

        # 最终统计
        print_stats()
        print("[MAIN] 程序退出")

if __name__ == "__main__":
    main()
