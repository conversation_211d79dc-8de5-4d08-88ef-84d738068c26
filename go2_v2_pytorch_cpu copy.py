#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RTSP流垃圾检测 + HTTP多路推流
结合 i_local.py 和 new_main.py 的功能
- 从RTSP流获取视频
- YOLOv8垃圾检测 (GPU加速)
- HTTP MJPEG多路流推送，支持原始流和处理后流
"""

import cv2
import torch
import numpy as np
from ultralytics import YOLO
from threading import Thread, Lock
from queue import Queue, Empty
from time import time, sleep
import os
import warnings
import socket
import http.server
from functools import partial # Used to pass arguments to the HTTP handler

# Environment configuration to suppress logs
os.environ['OPENCV_LOG_LEVEL'] = 'SILENT'
os.environ['OPENCV_VIDEOIO_DEBUG'] = '0'
os.environ['FFMPEG_LOG_LEVEL'] = 'quiet'
os.environ['AV_LOG_FORCE_NOCOLOR'] = '1'
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

warnings.filterwarnings('ignore')
cv2.setLogLevel(0)

device = 'cuda' if torch.cuda.is_available() else 'cpu'

print("=" * 50)
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA device count: {torch.cuda.device_count()}")
    print(f"CUDA device name: {torch.cuda.get_device_name(0)}")
    print(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
print(f"Using device: {device}")
print(f"OpenCV version: {cv2.__version__}")
print("=" * 50)

class Config:
    RTSP_URL = "rtsp://192.168.0.186:8554/camera"
    RTSP_RECONNECT_MAX = 5
    RTSP_BUFFER_SIZE = 1

    CONFIDENCE_THRESHOLD = 0.5
    MODEL_PATH = 'yolov8n.pt'

    QUEUE_SIZE = 3
    STATS_INTERVAL = 30
    HTTP_FPS = 20

    STREAM_CONFIGS = [
        {"name": "Detection Result Stream", "port": 5000, "path": "/video_feed", "source": "processed"},
        {"name": "Raw Camera Stream", "port": 5001, "path": "/video_feed", "source": "raw"}
    ]
    ENABLE_DISPLAY = False  # 关闭显示，避免GUI相关代码

garbage_classes = {}
CONFIDENCE_THRESHOLD = Config.CONFIDENCE_THRESHOLD
QUEUE_SIZE = Config.QUEUE_SIZE
STATS_INTERVAL = Config.STATS_INTERVAL

model = YOLO(Config.MODEL_PATH)
if device == 'cuda':
    model.to(device)
    print(f"[DEBUG] Model loaded to GPU: {device}")
else:
    print(f"[DEBUG] Model loaded to CPU: {device}")
print("[DEBUG] Model loaded successfully")

Config.GARBAGE_CLASSES = model.names
garbage_classes = Config.GARBAGE_CLASSES
print(f"[INFO] All categories supported by the model: {Config.GARBAGE_CLASSES}")

current_processed_frame = None
current_raw_frame = None
frame_lock = Lock()
frame_queue = Queue(maxsize=QUEUE_SIZE)

stats = {
    'frames_received': 0,
    'frames_processed': 0,
    'frames_displayed': 0,
    'valid_frames': 0,
    'error_frames': 0,
    'queue_drops': 0,
    'start_time': time()
}

def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.settimeout(2)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception as e:
        print(f"[WARNING] Failed to get IP: {e}, using default IP address 127.0.0.1")
        return "127.0.0.1"

def create_test_frame():
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    cv2.putText(frame, "RTSP Stream Not Available", (150, 200),
                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    cv2.putText(frame, "Showing Test Pattern", (200, 250),
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
    cv2.putText(frame, f"Time: {int(time())}", (250, 300),
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    cv2.circle(frame, (320, 350), 50, (0, 0, 255), 3)
    cv2.rectangle(frame, (250, 400), (390, 450), (255, 0, 0), 3)
    return frame

def is_frame_valid(frame):
    if frame is None or frame.size == 0:
        return False
    if len(frame.shape) != 3 or frame.shape[2] != 3:
        return False
    if frame.shape[0] < 100 or frame.shape[1] < 100:
        return False
    mean_val = np.mean(frame)
    if mean_val < 10 or mean_val > 245:
        return False
    return True

def draw_detections(frame, detections):
    for x1, y1, x2, y2, label in detections:
        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
        cv2.rectangle(frame, (x1, y1 - label_size[1] - 10),
                      (x1 + label_size[0], y1), (0, 255, 0), -1)
        cv2.putText(frame, label, (x1, y1 - 5),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    return frame

class DynamicStreamingHandler(http.server.BaseHTTPRequestHandler):
    def __init__(self, *args, source_type, **kwargs):
        self.source_type = source_type
        super().__init__(*args, **kwargs)

    def do_GET(self):
        global current_processed_frame, current_raw_frame

        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.end_headers()

            local_ip = get_local_ip()
            stream_links_html = ""
            for stream_cfg in Config.STREAM_CONFIGS:
                stream_url = f"http://{local_ip}:{stream_cfg['port']}{stream_cfg['path']}"
                stream_home_url = f"http://{local_ip}:{stream_cfg['port']}/"
                stream_links_html += f"""
                <div class="stream-card">
                    <h3>{stream_cfg['name']}</h3>
                    <p>Port: {stream_cfg['port']}</p>
                    <p>Source Type: {stream_cfg['source'].capitalize()}</p>
                    <p><a href="{stream_url}" target="_blank">Click here to view video stream (new window)</a></p>
                    <p><a href="{stream_home_url}" target="_self">Return to this port's homepage</a></p>
                    <img src="{stream_url}" alt="{stream_cfg['name']}" style="max-width: 90%; border-radius: 5px;">
                </div>
                """

            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>RTSP Garbage Detection Multi-Stream Server</title>
                <style>
                    body {{ font-family: 'Inter', Arial, sans-serif; text-align: center; background: #f0f2f5; color: #333; margin: 0; padding: 20px; }}
                    .container {{ max-width: 1200px; margin: 20px auto; padding: 30px; background: #ffffff; border-radius: 12px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); }}
                    h1 {{ color: #2c3e50; margin-bottom: 25px; font-size: 2.5em; }}
                    .info {{ background: #e9f7ef; color: #28a745; padding: 15px; margin: 20px 0; border-radius: 8px; border: 1px solid #d4edda; font-size: 1.1em; }}
                    .info p {{ margin: 5px 0; }}
                    h2 {{ color: #34495e; margin-top: 40px; margin-bottom: 20px; font-size: 2em; }}
                    .stream-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 30px; margin-top: 30px; }}
                    .stream-card {{ background: #fdfdfd; border: 1px solid #e0e0e0; border-radius: 10px; padding: 20px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05); text-align: left; }}
                    .stream-card h3 {{ color: #007bff; margin-top: 0; font-size: 1.5em; }}
                    .stream-card p {{ margin: 8px 0; font-size: 1em; }}
                    .stream-card a {{ color: #007bff; text-decoration: none; font-weight: bold; }}
                    .stream-card a:hover {{ text-decoration: underline; }}
                    img {{ border: 2px solid #ddd; border-radius: 8px; max-width: 100%; height: auto; display: block; margin: 15px auto 0; }}
                    .tip {{ margin-top: 30px; font-style: italic; color: #666; font-size: 0.9em; }}
                    @media (max-width: 768px) {{
                        .stream-grid {{ grid-template-columns: 1fr; }}
                        .container {{ padding: 20px; }}
                    }}
                </style>
                <script src="https://cdn.tailwindcss.com"></script>
            </head>
            <body>
                <div class="container">
                    <h1>🗑️ RTSP Garbage Detection Multi-Stream Server</h1>
                    <div class="info">
                        <p>Real-time detection targets: All available COCO classes</p>
                        <p>RTSP Source: {Config.RTSP_URL}</p>
                        <p>Local IP Address: {local_ip}</p>
                    </div>
                    <h2>Available Video Streams:</h2>
                    <div class="stream-grid">
                        {stream_links_html}
                    </div>
                    <p class="tip">💡 Tip: Each video stream runs on a separate HTTP service port.</p>
                </div>
            </body>
            </html>
            """
            self.wfile.write(html.encode())

        elif self.path == '/video_feed':
            self.send_response(200)
            self.send_header('Content-Type', 'multipart/x-mixed-replace; boundary=frame')
            self.send_header('Cache-Control', 'no-cache')
            self.send_header('Connection', 'close')
            self.end_headers()

            frame_count = 0
            while True:
                try:
                    frame_to_send = None
                    with frame_lock:
                        if self.source_type == "processed":
                            frame_to_send = current_processed_frame.copy() if current_processed_frame is not None else None
                        elif self.source_type == "raw":
                            frame_to_send = current_raw_frame.copy() if current_raw_frame is not None else None

                    if frame_to_send is None:
                        frame_to_send = create_test_frame()
                        cv2.putText(frame_to_send, f"Stream Type: {self.source_type.capitalize()} | Frame: {frame_count}", (20, 60),
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 2)

                    ret, buffer = cv2.imencode('.jpg', frame_to_send, [cv2.IMWRITE_JPEG_QUALITY, 85])
                    if ret:
                        frame_bytes = buffer.tobytes()
                        self.wfile.write(b'--frame\r\n')
                        self.wfile.write(b'Content-Type: image/jpeg\r\n')
                        self.wfile.write(f'Content-Length: {len(frame_bytes)}\r\n\r\n'.encode())
                        self.wfile.write(frame_bytes)
                        self.wfile.write(b'\r\n')
                        self.wfile.flush()

                        frame_count += 1

                    sleep(1.0 / Config.HTTP_FPS)

                except ConnectionResetError:
                    break
                except BrokenPipeError:
                    break
                except Exception as e:
                    if "connection" not in str(e).lower() and "winerror 10038" not in str(e).lower():
                        print(f"[HTTP] Stream transmission error ({self.source_type}): {e}")
                    break
        else:
            self.send_response(404)
            self.end_headers()

    def log_message(self, format, *args):
        pass  # Suppress logs

def process_frames():
    global current_processed_frame

    while True:
        try:
            frame = frame_queue.get(timeout=5)

            if not is_frame_valid(frame):
                stats['error_frames'] += 1
                continue

            stats['valid_frames'] += 1

            results = model(frame, imgsz=416, conf=CONFIDENCE_THRESHOLD, verbose=False, device=device)[0]
            detections = []

            for box in results.boxes:
                class_id = int(box.cls)
                if class_id in Config.GARBAGE_CLASSES:
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    confidence = float(box.conf)
                    label = f"{Config.GARBAGE_CLASSES[class_id]} {confidence:.2f}"
                    detections.append((x1, y1, x2, y2, label))

            processed_frame = draw_detections(frame.copy(), detections)
            cv2.putText(processed_frame, f"Objects: {len(detections)} | Device: {device}",
                        (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            cv2.putText(processed_frame, f"IP: {get_local_ip()}",
                        (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

            with frame_lock:
                current_processed_frame = processed_frame

            stats['frames_processed'] += 1

        except Empty:
            print("[WARNING] Frame processing thread timed out waiting for queue, no frames to process.")
            continue
        except Exception as e:
            print(f"[PROCESS_THREAD] Frame processing thread exception: {e}")
            break

    print("[PROCESS_THREAD] Frame processing thread exited.")

def start_http_server(port, handler_class, server_name="HTTP Server"):
    try:
        from http.server import HTTPServer
        httpd = HTTPServer(("0.0.0.0", port), handler_class)
        local_ip = get_local_ip()
        print(f"[{server_name}] Server starting on port {port}...")
        print(f"[{server_name}] Local access: http://localhost:{port}/")
        print(f"[{server_name}] LAN access: http://{local_ip}:{port}/")
        print(f"[{server_name}] Direct video stream URL: http://{local_ip}:{port}/video_feed")

        httpd.timeout = 1.0

        print(f"[{server_name}] Server listening...")
        while True:
            try:
                httpd.handle_request()
            except KeyboardInterrupt:
                print(f"\n[{server_name}] Received exit signal, server stopping.")
                break
            except Exception as e:
                if "connection" not in str(e).lower() and "winerror 10038" not in str(e).lower():
                    print(f"[{server_name}] Request processing error: {e}")
                continue

    except Exception as e:
        print(f"[{server_name}] Server failed to start: {e}")
        import traceback
        traceback.print_exc()

def print_stats():
    elapsed = time() - stats['start_time']
    fps_received = stats['frames_received'] / elapsed if elapsed > 0 else 0
    fps_processed = stats['frames_processed'] / elapsed if elapsed > 0 else 0
    fps_displayed = stats['frames_displayed'] / elapsed if elapsed > 0 else 0
    valid_rate = (stats['valid_frames'] / stats['frames_received'] * 100) if stats['frames_received'] else 0
    queue_drop_rate = (stats['queue_drops'] / stats['frames_received'] * 100) if stats['frames_received'] else 0

    print(f"====== Statistics (Last {STATS_INTERVAL}s) ======")
    print(f"Frames Received: {stats['frames_received']} (FPS: {fps_received:.2f})")
    print(f"Frames Processed: {stats['frames_processed']} (FPS: {fps_processed:.2f})")
    print(f"Frames Displayed: {stats['frames_displayed']} (FPS: {fps_displayed:.2f})")
    print(f"Valid Frame Rate: {valid_rate:.2f}%")
    print(f"Error Frames: {stats['error_frames']}")
    print(f"Queue Drops: {stats['queue_drops']} ({queue_drop_rate:.2f}%)")
    print("="*40)

def rtsp_capture_loop(rtsp_url):
    global current_raw_frame
    reconnect_attempts = 0

    while True:
        cap = cv2.VideoCapture(rtsp_url)
        if not cap.isOpened():
            print(f"[RTSP] Unable to open stream: {rtsp_url}")
            reconnect_attempts += 1
            if reconnect_attempts >= Config.RTSP_RECONNECT_MAX:
                print("[RTSP] Maximum reconnect attempts reached, exiting capture loop.")
                break
            sleep(5)
            continue

        reconnect_attempts = 0
        print(f"[RTSP] Stream opened: {rtsp_url}")

        while cap.isOpened():
            ret, frame = cap.read()
            if not ret or frame is None:
                print("[RTSP] Frame read failed or empty frame received, reconnecting...")
                break

            stats['frames_received'] += 1

            with frame_lock:
                current_raw_frame = frame.copy()

            try:
                if frame_queue.full():
                    try:
                        frame_queue.get_nowait()
                        stats['queue_drops'] += 1
                    except Empty:
                        pass

                frame_queue.put_nowait(frame.copy())
            except Exception as e:
                print(f"[RTSP] Frame queue error: {e}")

        cap.release()
        sleep(2)  # Wait before reconnecting

def main():
    import threading

    # Start frame processing thread
    processing_thread = threading.Thread(target=process_frames, daemon=True)
    processing_thread.start()

    # Start RTSP capture thread
    rtsp_thread = threading.Thread(target=rtsp_capture_loop, args=(Config.RTSP_URL,), daemon=True)
    rtsp_thread.start()

    # Start HTTP servers for each stream config
    server_threads = []
    for stream_cfg in Config.STREAM_CONFIGS:
        handler = partial(DynamicStreamingHandler, source_type=stream_cfg['source'])
        thread = threading.Thread(target=start_http_server, args=(stream_cfg['port'], handler, stream_cfg['name']), daemon=True)
        thread.start()
        server_threads.append(thread)

    last_stats_time = time()

    # 主循环，不显示GUI，防止报错
    try:
        while True:
            sleep(0.05)  # 适当睡眠，降低CPU占用

            now = time()
            if now - last_stats_time >= STATS_INTERVAL:
                print_stats()
                last_stats_time = now

    except KeyboardInterrupt:
        print("\n[MAIN] KeyboardInterrupt received, exiting...")

    # 等待线程退出（守护线程自动退出）
    print("[MAIN] Program finished.")

if __name__ == "__main__":
    main()
