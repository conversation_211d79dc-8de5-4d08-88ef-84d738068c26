#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_v10_web.py - 测试v10.py的Web界面功能
验证新增的按钮和当前物品显示功能
"""

import http.server
import socketserver
import json
import time

# 模拟数据
stats = {
    'frames_received': 1234,
    'frames_processed': 1200,
    'valid_frames': 1180,
    'error_frames': 20,
    'detections_total': 45,
    'detections_current': 2,
    'model_inference_time': 0.015,
    'current_objects': ['person', 'car', 'bicycle'],  # 测试当前物品显示
    'elapsed_time': 120,
    'fps_received': 25.5,
    'fps_processed': 24.8,
    'start_time': time.time()
}

system_info = {
    'device': 'cuda',
    'model_loaded': True,
    'rtsp_connected': True,
    'yolo_classes': ['person', 'bicycle', 'car', 'motorcycle'] * 20  # 80个类别
}

detection_enabled = False

class TestWebHandler(http.server.BaseHTTPRequestHandler):
    """测试Web界面处理器"""
    
    def do_GET(self):
        if self.path == '/':
            self.send_main_page()
        else:
            self.send_error(404)
    
    def do_POST(self):
        global detection_enabled
        
        if self.path == '/api/start':
            detection_enabled = True
            response = {"status": "success", "message": "Detection started"}
        elif self.path == '/api/stop':
            detection_enabled = False
            response = {"status": "success", "message": "Detection stopped"}
        elif self.path == '/api/status':
            response = {
                "status": "success",
                "detection_enabled": detection_enabled,
                "processing_active": detection_enabled,
                "stats": stats,
                "system_info": system_info
            }
        else:
            response = {"status": "error", "message": "Unknown API endpoint"}
        
        # 发送JSON响应
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())
    
    def send_main_page(self):
        """发送主页"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>检测控制台 - 测试版</title>
            <style>
                body {{ font-family: Arial, sans-serif; background: #f0f2f5; color: #333; margin: 0; padding: 20px; }}
                .container {{ max-width: 1200px; margin: 0 auto; }}
                h1 {{ text-align: center; color: #2c3e50; margin-bottom: 30px; }}
                .main-content {{ display: flex; gap: 20px; }}
                .left-panel, .right-panel {{ flex: 1; background: #ffffff; padding: 20px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); }}
                .panel-title {{ font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 5px; }}
                .info-item {{ margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 5px; }}
                .info-label {{ font-weight: bold; color: #555; }}
                .info-value {{ color: #333; }}
                .controls {{ text-align: center; margin: 30px 0; }}
                .status {{ padding: 15px; margin: 20px 0; border-radius: 8px; font-weight: bold; text-align: center; }}
                .btn {{ padding: 12px 24px; margin: 8px; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: bold; transition: all 0.3s; }}
                .btn-start {{ background: #28a745; color: white; }}
                .btn-start:hover {{ background: #218838; }}
                .btn-stop {{ background: #dc3545; color: white; }}
                .btn-stop:hover {{ background: #c82333; }}
                .btn-refresh {{ background: #007bff; color: white; }}
                .btn-refresh:hover {{ background: #0056b3; }}
                .btn-video {{ background: #17a2b8; color: white; }}
                .btn-video:hover {{ background: #138496; }}
                .stats-grid {{ display: grid; grid-template-columns: 1fr 1fr; gap: 10px; }}
                .detection-highlight {{ background: #e8f5e8; border-left: 4px solid #28a745; }}
                .current-objects {{ background: #fff3cd; border-left: 4px solid #ffc107; padding: 10px; margin-top: 10px; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>检测控制台 - 测试版</h1>
                
                <div class="status" id="status">检测状态: 加载中...</div>
                
                <div class="controls">
                    <button class="btn btn-start" onclick="startDetection()">🚀 启动YOLO检测</button>
                    <button class="btn btn-stop" onclick="stopDetection()">🛑 停止检测</button>
                    <button class="btn btn-video" onclick="openVideoFeed()">📺 打开Web端视频</button>
                    <button class="btn btn-refresh" onclick="updateStatus()">🔄 刷新状态</button>
                </div>
                
                <div class="main-content">
                    <div class="left-panel">
                        <div class="panel-title">📊 系统信息</div>
                        <div id="system-info">
                            <div class="info-item">
                                <span class="info-label">本地IP:</span>
                                <span class="info-value" id="local-ip">*************</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">计算设备:</span>
                                <span class="info-value" id="device">检测中...</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">模型状态:</span>
                                <span class="info-value" id="model-status">检测中...</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">RTSP源:</span>
                                <span class="info-value" id="rtsp-url">检测中...</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">支持类别:</span>
                                <span class="info-value" id="yolo-classes">检测中...</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">置信度阈值:</span>
                                <span class="info-value">0.3</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="right-panel">
                        <div class="panel-title">📈 实时检测信息</div>
                        <div id="detection-info">
                            <div class="stats-grid">
                                <div class="info-item">
                                    <span class="info-label">运行时间:</span>
                                    <span class="info-value" id="elapsed-time">0秒</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">接收帧数:</span>
                                    <span class="info-value" id="frames-received">0</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">处理帧数:</span>
                                    <span class="info-value" id="frames-processed">0</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">有效帧数:</span>
                                    <span class="info-value" id="valid-frames">0</span>
                                </div>
                                <div class="info-item detection-highlight">
                                    <span class="info-label">当前检测数:</span>
                                    <span class="info-value" id="detections-current">0</span>
                                </div>
                                <div class="info-item detection-highlight">
                                    <span class="info-label">总检测数:</span>
                                    <span class="info-value" id="detections-total">0</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">推理时间:</span>
                                    <span class="info-value" id="inference-time">0ms</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">处理FPS:</span>
                                    <span class="info-value" id="fps-processed">0.0</span>
                                </div>
                            </div>
                            <div class="current-objects" id="current-objects-panel">
                                <div class="info-label">当前检测物品:</div>
                                <div class="info-value" id="current-objects">无</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <script>
                function updateStatus() {{
                    fetch('/api/status', {{method: 'POST'}})
                        .then(response => response.json())
                        .then(data => {{
                            // 更新检测状态
                            const statusDiv = document.getElementById('status');
                            if (data.detection_enabled) {{
                                statusDiv.style.background = '#28a745';
                                statusDiv.style.color = 'white';
                                statusDiv.innerHTML = '检测状态: <strong>🟢 YOLO检测启用中</strong>';
                            }} else {{
                                statusDiv.style.background = '#dc3545';
                                statusDiv.style.color = 'white';
                                statusDiv.innerHTML = '检测状态: <strong>🔴 检测禁用</strong>';
                            }}
                            
                            // 更新系统信息
                            const sysInfo = data.system_info;
                            document.getElementById('device').textContent = sysInfo.device.toUpperCase() + (sysInfo.device === 'cuda' ? ' (GPU加速)' : ' (CPU模式)');
                            document.getElementById('model-status').textContent = sysInfo.model_loaded ? '✅ 已加载' : '❌ 未加载';
                            document.getElementById('rtsp-url').textContent = 'rtsp://192.168.0.186:8554/camera';
                            document.getElementById('yolo-classes').textContent = sysInfo.yolo_classes.length + ' 个类别';
                            
                            // 更新检测信息
                            const stats = data.stats;
                            document.getElementById('elapsed-time').textContent = Math.floor(stats.elapsed_time) + '秒';
                            document.getElementById('frames-received').textContent = stats.frames_received;
                            document.getElementById('frames-processed').textContent = stats.frames_processed;
                            document.getElementById('valid-frames').textContent = stats.valid_frames;
                            document.getElementById('detections-current').textContent = stats.detections_current || 0;
                            document.getElementById('detections-total').textContent = stats.detections_total || 0;
                            document.getElementById('inference-time').textContent = (stats.model_inference_time * 1000).toFixed(1) + 'ms';
                            document.getElementById('fps-processed').textContent = stats.fps_processed.toFixed(1);
                            
                            // 更新当前检测物品信息 - 这是新功能！
                            const currentObjects = stats.current_objects || [];
                            const objectsDiv = document.getElementById('current-objects');
                            if (currentObjects.length === 0) {{
                                objectsDiv.textContent = '无';
                                objectsDiv.style.color = '#666';
                            }} else {{
                                let displayText = '';
                                if (currentObjects.length <= 2) {{
                                    // 两个以内全显示
                                    displayText = currentObjects.join(', ');
                                }} else {{
                                    // 超过两个显示两个，后面写个"等"
                                    displayText = currentObjects.slice(0, 2).join(', ') + ' 等';
                                }}
                                objectsDiv.textContent = displayText;
                                objectsDiv.style.color = '#28a745';
                                objectsDiv.style.fontWeight = 'bold';
                            }}
                            
                            console.log('状态更新完成 - 当前物品:', currentObjects);
                        }})
                        .catch(error => {{
                            console.error('状态更新失败:', error);
                            document.getElementById('status').innerHTML = '检测状态: <strong>❌ 更新失败</strong>';
                        }});
                }}
                
                function startDetection() {{
                    fetch('/api/start', {{method: 'POST'}})
                        .then(response => response.json())
                        .then(data => {{
                            alert('✅ ' + data.message);
                            updateStatus();
                        }})
                        .catch(error => {{
                            alert('❌ 启动失败: ' + error);
                        }});
                }}
                
                function stopDetection() {{
                    fetch('/api/stop', {{method: 'POST'}})
                        .then(response => response.json())
                        .then(data => {{
                            alert('✅ ' + data.message);
                            updateStatus();
                        }})
                        .catch(error => {{
                            alert('❌ 停止失败: ' + error);
                        }});
                }}
                
                function openVideoFeed() {{
                    // 打开Web端视频流 - 这是新功能！
                    const videoUrl = 'http://*************:5000/video_feed';
                    alert('🎬 将打开Web端视频:\\n' + videoUrl + '\\n\\n(测试版本，实际会在新窗口打开)');
                    // window.open(videoUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
                }}
                
                // 页面加载时立即更新状态
                document.addEventListener('DOMContentLoaded', function() {{
                    updateStatus();
                    // 每3秒自动更新状态
                    setInterval(updateStatus, 3000);
                }});
            </script>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode())
    
    def log_message(self, fmt, *args):
        # 抑制HTTP日志
        pass

def main():
    """主函数"""
    port = 5002  # 使用不同端口避免冲突
    
    print(f"🧪 启动v10.py Web界面测试服务器")
    print(f"📺 测试地址: http://*************:{port}")
    print(f"🎯 测试功能:")
    print(f"   - 新增'打开Web端视频'按钮")
    print(f"   - 实时检测信息栏显示当前物品")
    print(f"   - 两个以内全显示，超过两个显示两个+等")
    
    try:
        with socketserver.TCPServer(("", port), TestWebHandler) as httpd:
            print(f"✅ 测试服务器启动成功！")
            print(f"💡 打开浏览器访问: http://*************:{port}")
            httpd.serve_forever()
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

if __name__ == "__main__":
    main()
