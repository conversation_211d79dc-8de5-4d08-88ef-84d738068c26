#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_v12_simple.py - 简化的main_v12.py多线程测试
不依赖RTSP，专门测试多线程HTTP服务器功能
"""

import http.server
import socketserver
import json
import threading
import time
import requests
import concurrent.futures

# 多线程HTTP服务器类
class ThreadedHTTPServer(socketserver.ThreadingMixIn, socketserver.TCPServer):
    """多线程HTTP服务器 - 解决视频流阻塞API请求的问题"""
    allow_reuse_address = True
    daemon_threads = True

class TestWebHandler(http.server.BaseHTTPRequestHandler):
    """测试Web处理器"""
    
    def do_GET(self):
        if self.path == '/':
            self.send_main_page()
        elif self.path == '/video_feed':
            self.send_video_feed()
        else:
            self.send_error(404)
    
    def do_POST(self):
        thread_id = threading.current_thread().ident
        
        if self.path == '/api/start':
            response = {
                "status": "success",
                "message": "Detection started",
                "video_url": f"http://*************:5001/video_feed",
                "thread_id": thread_id,
                "server_type": "multi-threaded"
            }
            print(f"[API] 启动检测 - 线程ID: {thread_id}")
        elif self.path == '/api/stop':
            response = {
                "status": "success",
                "message": "Detection stopped",
                "thread_id": thread_id,
                "server_type": "multi-threaded"
            }
            print(f"[API] 停止检测 - 线程ID: {thread_id}")
        elif self.path == '/api/status':
            response = {
                "status": "success",
                "thread_id": thread_id,
                "server_type": "multi-threaded",
                "timestamp": time.strftime('%H:%M:%S')
            }
            print(f"[API] 状态查询 - 线程ID: {thread_id}")
        else:
            response = {"status": "error", "message": "Unknown endpoint"}
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())
    
    def send_main_page(self):
        html = """
        <!DOCTYPE html>
        <html>
        <head><title>多线程测试服务器</title></head>
        <body>
            <h1>多线程HTTP服务器测试</h1>
            <button onclick="startDetection()">启动检测</button>
            <button onclick="stopDetection()">停止检测</button>
            <button onclick="openVideo()">打开视频</button>
            <script>
                function startDetection() {
                    fetch('/api/start', {method: 'POST'})
                        .then(r => r.json())
                        .then(data => alert('启动成功 - 线程ID: ' + data.thread_id));
                }
                function stopDetection() {
                    fetch('/api/stop', {method: 'POST'})
                        .then(r => r.json())
                        .then(data => alert('停止成功 - 线程ID: ' + data.thread_id));
                }
                function openVideo() {
                    window.open('/video_feed', '_blank');
                }
            </script>
        </body>
        </html>
        """
        self.send_response(200)
        self.send_header('Content-Type', 'text/html')
        self.end_headers()
        self.wfile.write(html.encode())
    
    def send_video_feed(self):
        """模拟视频流 - 长时间运行"""
        thread_id = threading.current_thread().ident
        print(f"[VIDEO] 视频流开始 - 线程ID: {thread_id}")
        
        self.send_response(200)
        self.send_header('Content-Type', 'text/plain')
        self.end_headers()
        
        try:
            for i in range(100):  # 模拟100帧
                self.wfile.write(f"Frame {i+1} - Thread {thread_id}\n".encode())
                self.wfile.flush()
                time.sleep(0.1)  # 每100ms一帧
        except Exception as e:
            print(f"[VIDEO] 视频流结束 - 线程ID: {thread_id}, 错误: {e}")
    
    def log_message(self, fmt, *args):
        pass

def test_concurrent_requests():
    """测试并发请求"""
    print("\n🧪 测试并发请求...")
    
    def make_api_call(endpoint, test_id):
        try:
            response = requests.post(f"http://*************:5001/api/{endpoint}", timeout=3)
            data = response.json()
            print(f"[TEST-{test_id}] {endpoint} 成功 - 线程ID: {data.get('thread_id')}")
            return True
        except Exception as e:
            print(f"[TEST-{test_id}] {endpoint} 失败 - {e}")
            return False
    
    def access_video_stream(test_id):
        try:
            response = requests.get("http://*************:5001/video_feed", timeout=5, stream=True)
            if response.status_code == 200:
                # 读取一些数据
                for i, chunk in enumerate(response.iter_content(chunk_size=1024)):
                    if i > 10:  # 读取几个chunk就停止
                        break
                print(f"[VIDEO-{test_id}] 视频流访问成功")
                return True
        except Exception as e:
            print(f"[VIDEO-{test_id}] 视频流访问失败 - {e}")
            return False
    
    # 使用线程池进行并发测试
    with concurrent.futures.ThreadPoolExecutor(max_workers=6) as executor:
        futures = []
        
        # 启动视频流访问
        futures.append(executor.submit(access_video_stream, 1))
        
        # 等待0.5秒，确保视频流开始
        time.sleep(0.5)
        
        # 在视频流运行期间进行API调用
        for i in range(5):
            futures.append(executor.submit(make_api_call, 'status', i+1))
            time.sleep(0.2)
        
        # 等待所有任务完成
        results = [f.result() for f in concurrent.futures.as_completed(futures, timeout=10)]
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n📊 并发测试结果: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        print("✅ 多线程服务器工作正常，无阻塞问题！")
        return True
    else:
        print("❌ 存在阻塞问题")
        return False

def main():
    """主函数"""
    print("🧪 main_v12.py 多线程功能简化测试")
    print("=" * 50)
    
    port = 5001
    
    # 启动测试服务器
    print(f"启动多线程测试服务器在端口 {port}...")
    httpd = ThreadedHTTPServer(("", port), TestWebHandler)
    
    # 在后台线程中运行服务器
    server_thread = threading.Thread(target=httpd.serve_forever, daemon=True)
    server_thread.start()
    
    print(f"✅ 服务器启动成功: http://*************:{port}")
    
    # 等待服务器启动
    time.sleep(1)
    
    try:
        # 运行并发测试
        success = test_concurrent_requests()
        
        if success:
            print("\n🎉 多线程HTTP服务器功能验证成功！")
            print("✅ 视频流和API请求可以并发处理")
            print("✅ main_v12.py的多线程设计是正确的")
        else:
            print("\n❌ 多线程功能存在问题")
        
    finally:
        # 关闭服务器
        httpd.shutdown()
        httpd.server_close()
        print("\n🔄 测试服务器已关闭")

if __name__ == "__main__":
    main()
