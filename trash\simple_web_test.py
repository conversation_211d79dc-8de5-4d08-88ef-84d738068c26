#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Web界面状态更新测试
"""

import http.server
import socketserver
import json
import threading
import time

# 全局状态变量
detection_enabled = False
processing_active = False
stats = {
    'frames_received': 0,
    'frames_processed': 0,
    'start_time': time.time()
}

class SimpleHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.send_main_page()
        else:
            self.send_error(404)
    
    def do_POST(self):
        global detection_enabled, processing_active
        
        if self.path == '/api/start':
            detection_enabled = True
            processing_active = True
            response = {"status": "success", "message": "Detection started"}
            print(f"[API] Detection started - enabled: {detection_enabled}")
        elif self.path == '/api/stop':
            detection_enabled = False
            processing_active = False
            response = {"status": "success", "message": "Detection stopped"}
            print(f"[API] Detection stopped - enabled: {detection_enabled}")
        elif self.path == '/api/status':
            response = {
                "status": "success",
                "detection_enabled": detection_enabled,
                "processing_active": processing_active,
                "stats": stats
            }
        else:
            response = {"status": "error", "message": "Unknown API endpoint"}
        
        # 发送JSON响应
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())
    
    def send_main_page(self):
        """发送主页"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Web状态更新测试</title>
            <style>
                body {{ font-family: Arial, sans-serif; text-align: center; padding: 20px; }}
                .status {{ padding: 15px; margin: 20px; border-radius: 5px; font-weight: bold; }}
                .btn {{ padding: 10px 20px; margin: 10px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }}
                .btn-start {{ background: #28a745; color: white; }}
                .btn-stop {{ background: #dc3545; color: white; }}
            </style>
        </head>
        <body>
            <h1>🧪 Web状态更新测试</h1>
            <div id="status" class="status">状态: 加载中...</div>
            <div>
                <button class="btn btn-start" onclick="startDetection()">启动检测</button>
                <button class="btn btn-stop" onclick="stopDetection()">停止检测</button>
                <button class="btn" onclick="updateStatus()">刷新状态</button>
            </div>
            <div id="info" style="margin-top: 20px; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                <p>点击按钮测试状态更新功能</p>
                <p>状态应该在点击后立即更新</p>
            </div>
            
            <script>
                function updateStatus() {{
                    fetch('/api/status', {{method: 'POST'}})
                        .then(response => response.json())
                        .then(data => {{
                            const statusDiv = document.getElementById('status');
                            
                            if (data.detection_enabled) {{
                                statusDiv.style.background = '#28a745';
                                statusDiv.style.color = 'white';
                                statusDiv.innerHTML = '状态: <strong>🟢 检测启用</strong>';
                            }} else {{
                                statusDiv.style.background = '#dc3545';
                                statusDiv.style.color = 'white';
                                statusDiv.innerHTML = '状态: <strong>🔴 检测禁用</strong>';
                            }}
                            
                            console.log('状态更新:', data);
                        }})
                        .catch(error => {{
                            console.error('状态更新失败:', error);
                            document.getElementById('status').innerHTML = '状态: ❌ 更新失败';
                        }});
                }}
                
                function startDetection() {{
                    fetch('/api/start', {{method: 'POST'}})
                        .then(response => response.json())
                        .then(data => {{
                            alert(data.message);
                            updateStatus(); // 立即更新状态
                        }})
                        .catch(error => {{
                            alert('启动失败: ' + error);
                        }});
                }}
                
                function stopDetection() {{
                    fetch('/api/stop', {{method: 'POST'}})
                        .then(response => response.json())
                        .then(data => {{
                            alert(data.message);
                            updateStatus(); // 立即更新状态
                        }})
                        .catch(error => {{
                            alert('停止失败: ' + error);
                        }});
                }}
                
                // 页面加载时更新状态
                window.onload = function() {{
                    updateStatus();
                    // 每3秒自动更新状态
                    setInterval(updateStatus, 3000);
                }};
            </script>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode())
    
    def log_message(self, fmt, *args):
        print(f"[HTTP] {fmt % args}")

def simulate_processing():
    """模拟处理过程"""
    global stats
    
    while True:
        time.sleep(1)
        stats['frames_received'] += 10
        
        if detection_enabled:
            stats['frames_processed'] += 8
        
        # 每10秒打印一次状态
        if int(time.time()) % 10 == 0:
            print(f"[SIM] 检测: {'启用' if detection_enabled else '禁用'}, "
                  f"接收: {stats['frames_received']}, 处理: {stats['frames_processed']}")

def main():
    PORT = 5000
    
    print("🧪 启动简单Web状态更新测试服务器")
    print(f"📺 访问: http://*************:{PORT}")
    print("=" * 50)
    
    # 启动模拟处理线程
    threading.Thread(target=simulate_processing, daemon=True).start()
    
    # 启动HTTP服务器
    try:
        with socketserver.TCPServer(("", PORT), SimpleHandler) as httpd:
            print(f"✅ 服务器启动成功，端口: {PORT}")
            httpd.serve_forever()
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

if __name__ == "__main__":
    main()
