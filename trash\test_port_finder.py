#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_port_finder.py - 测试main_v11.py的端口自动寻找功能
"""

import socket
import time
import subprocess
import sys
import os

def occupy_port(port):
    """占用指定端口"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        sock.bind(('', port))
        sock.listen(1)
        print(f"✅ 成功占用端口 {port}")
        return sock
    except Exception as e:
        print(f"❌ 无法占用端口 {port}: {e}")
        return None

def start_main_v11():
    """启动main_v11.py进程"""
    try:
        process = subprocess.Popen(
            [sys.executable, "main_v11.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        print(f"✅ main_v11.py进程已启动 (PID: {process.pid})")
        return process
    except Exception as e:
        print(f"❌ 启动main_v11.py失败: {e}")
        return None

def check_port_usage(start_port, end_port):
    """检查端口范围内哪些端口被使用"""
    used_ports = []
    
    for port in range(start_port, end_port + 1):
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(0.5)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            
            if result == 0:
                used_ports.append(port)
                print(f"🔍 端口 {port} 被占用")
            else:
                print(f"🔍 端口 {port} 空闲")
        except:
            print(f"🔍 端口 {port} 检测失败")
    
    return used_ports

def main():
    """主函数"""
    print("🧪 测试main_v11.py的端口自动寻找功能")
    print("=" * 50)
    
    # 检查初始端口使用情况
    print("\n1️⃣ 检查初始端口使用情况...")
    initial_used_ports = check_port_usage(5000, 5005)
    
    # 占用5000端口
    print("\n2️⃣ 占用5000端口...")
    sock_5000 = occupy_port(5000)
    if not sock_5000:
        print("❌ 无法占用5000端口，测试终止")
        return
    
    # 启动main_v11.py
    print("\n3️⃣ 启动main_v11.py...")
    process = start_main_v11()
    if not process:
        print("❌ 无法启动main_v11.py，测试终止")
        if sock_5000:
            sock_5000.close()
        return
    
    # 等待main_v11.py启动
    print("\n4️⃣ 等待main_v11.py启动...")
    time.sleep(5)
    
    # 检查端口使用情况
    print("\n5️⃣ 检查端口使用情况...")
    final_used_ports = check_port_usage(5000, 5005)
    
    # 分析结果
    new_used_ports = [p for p in final_used_ports if p not in initial_used_ports and p != 5000]
    
    print("\n" + "=" * 50)
    print("📊 测试结果")
    print("=" * 50)
    
    if new_used_ports:
        print(f"✅ main_v11.py成功自动选择了端口: {new_used_ports[0]}")
        print(f"✅ 端口自动寻找功能正常工作！")
    else:
        print("❌ 未检测到main_v11.py使用的新端口")
        print("❌ 端口自动寻找功能可能存在问题")
    
    # 清理资源
    print("\n6️⃣ 清理资源...")
    if process:
        process.terminate()
        try:
            process.wait(timeout=5)
            print("✅ main_v11.py进程已终止")
        except subprocess.TimeoutExpired:
            process.kill()
            print("✅ main_v11.py进程已强制终止")
    
    if sock_5000:
        sock_5000.close()
        print("✅ 端口5000已释放")
    
    print("=" * 50)
    print("🎯 测试完成")

if __name__ == "__main__":
    main()
