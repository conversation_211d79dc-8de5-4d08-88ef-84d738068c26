#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RTSP流垃圾检测 + HTTP多路推流
结合 i_local.py 和 new_main.py 的功能
- 从RTSP流获取视频
- YOLOv8垃圾检测 (GPU加速)
- HTTP MJPEG多路流推送，支持原始流和处理后流
"""

import cv2
import torch
import numpy as np
from ultralytics import YOLO
from threading import Thread, Lock
from queue import Queue, Empty
from time import time, sleep
import os
import warnings
import socket
import http.server
from functools import partial # 用于向HTTP处理程序传递参数

# 环境配置抑制日志
# 这些环境变量用于抑制OpenCV和FFmpeg的冗余日志输出
os.environ['OPENCV_LOG_LEVEL'] = 'SILENT'
os.environ['OPENCV_VIDEOIO_DEBUG'] = '0'
os.environ['FFMPEG_LOG_LEVEL'] = 'quiet'
os.environ['AV_LOG_FORCE_NOCOLOR'] = '1'
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

# 进一步抑制警告和OpenCV日志
warnings.filterwarnings('ignore')
cv2.setLogLevel(0)

# 设备检测和配置
# 检查CUDA是否可用，优先使用GPU进行PyTorch操作
device = 'cuda' if torch.cuda.is_available() else 'cpu'

# 打印环境信息，帮助用户了解当前运行环境
print("=" * 50)
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA device count: {torch.cuda.device_count()}")
    print(f"CUDA device name: {torch.cuda.get_device_name(0)}")
    print(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
print(f"Using device: {device}")
print(f"OpenCV version: {cv2.__version__}")
print("=" * 50)

# 配置类
# 集中管理所有可配置的参数
class Config:
    # RTSP配置
    RTSP_URL = "rtsp://192.168.0.186:8554/camera" # 你的RTSP视频流地址
    RTSP_RECONNECT_MAX = 5 # RTSP流最大重连次数
    RTSP_BUFFER_SIZE = 1 # OpenCV视频捕获的缓冲区大小，设置为1可减少延迟

    # 检测配置
    CONFIDENCE_THRESHOLD = 0.5 # YOLO检测的置信度阈值
    # GARBAGE_CLASSES 定义了你想要检测的垃圾类别及其对应的COCO数据集ID和名称
    # 如果要添加更多类别，请查找其在COCO数据集中的ID，并在此处添加
    # 例如：{39: 'bottle', 67: 'cell phone', 62: 'tv', 63: 'laptop'}
    GARBAGE_CLASSES = {39: 'bottle', 67: 'cell phone'} # 感兴趣的垃圾类别及其对应的COCO数据集ID和名称
    MODEL_PATH = 'yolov8n.pt' # YOLOv8模型文件路径，请确保该文件存在于脚本同目录下

    # 性能配置
    QUEUE_SIZE = 3  # 帧处理队列大小，减小可降低延迟但可能增加丢帧
    STATS_INTERVAL = 30  # 统计信息打印间隔（秒）
    HTTP_FPS = 20  # HTTP MJPEG流的推送帧率

    # HTTP配置 - 定义多个视频流，每个流可以有不同的端口和源类型
    # "source": "processed" 表示经过YOLO检测和绘制的帧
    # "source": "raw" 表示未经处理的原始帧
    STREAM_CONFIGS = [
        {"name": "检测结果流", "port": 5000, "path": "/video_feed", "source": "processed"},
        {"name": "原始摄像头流", "port": 5001, "path": "/video_feed", "source": "raw"}
    ]
    ENABLE_DISPLAY = False  # 是否启用OpenCV窗口显示（需要图形界面环境）

# 向后兼容（为了与旧代码片段保持一致，但建议直接使用Config类）
garbage_classes = Config.GARBAGE_CLASSES
CONFIDENCE_THRESHOLD = Config.CONFIDENCE_THRESHOLD
QUEUE_SIZE = Config.QUEUE_SIZE
STATS_INTERVAL = Config.STATS_INTERVAL

# 模型初始化
# 加载YOLOv8模型
model = YOLO(Config.MODEL_PATH)

# 将模型移动到指定设备（GPU或CPU）
if device == 'cuda':
    model.to(device)
    print(f"[DEBUG] 模型已加载到GPU: {device}")
else:
    print(f"[DEBUG] 模型已加载到CPU: {device}")

print("[DEBUG] 模型加载成功")
# 添加这行代码来打印模型支持的所有类别
# 这将显示模型（例如yolov8n.pt）默认或训练时支持的所有类别及其ID映射
print(f"[INFO] 模型支持的所有类别: {model.names}")


# 全局变量
# 用于存储最新处理后的帧和原始帧，并用锁保护并发访问
current_processed_frame = None
current_raw_frame = None
frame_lock = Lock() # 保护全局帧变量的互斥锁
frame_queue = Queue(maxsize=QUEUE_SIZE) # 待YOLO处理的帧队列

# 统计信息字典
stats = {
    'frames_received': 0, # 从RTSP接收到的总帧数
    'frames_processed': 0, # 经过YOLO处理的帧数
    'frames_displayed': 0, # 在OpenCV窗口中显示的帧数
    'valid_frames': 0, # 被认为是有效并进入处理流程的帧数
    'error_frames': 0, # 被判断为无效的错误帧数
    'queue_drops': 0, # 由于队列满而丢弃的帧数
    'start_time': time() # 程序启动时间
}

def get_local_ip():
    """
    获取本机局域网IP地址。
    尝试连接一个公共DNS服务器（不发送数据）以获取本机IP，
    如果失败则返回localhost IP作为备用。
    """
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.settimeout(2) # 设置超时
        s.connect(("*******", 80)) # 尝试连接公共DNS，获取本机出口IP
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception as e:
        print(f"[WARNING] 获取IP失败: {e}, 使用默认IP地址 127.0.0.1")
        return "127.0.0.1" # 无法获取IP时使用回环地址

def create_test_frame():
    """
    创建一个测试帧。
    当RTSP流不可用时，HTTP服务器将推送此测试帧。
    """
    frame = np.zeros((480, 640, 3), dtype=np.uint8) # 创建黑色背景帧
    
    # 添加提示文字
    # 修正：使用英文，避免中文乱码问题
    cv2.putText(frame, "RTSP Stream Not Available", (150, 200), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    cv2.putText(frame, "Showing Test Pattern", (200, 250), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
    cv2.putText(frame, f"Time: {int(time())}", (250, 300), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    # 添加简单的图形
    cv2.circle(frame, (320, 350), 50, (0, 0, 255), 3) # 红色圆圈
    cv2.rectangle(frame, (250, 400), (390, 450), (255, 0, 0), 3) # 蓝色矩形
    
    return frame

def is_frame_valid(frame):
    """
    检查捕获到的帧是否有效。
    判断条件包括：非None、非空、3通道彩色图像、尺寸足够大、像素平均值不在极值。
    """
    if frame is None or frame.size == 0:
        return False
    if len(frame.shape) != 3 or frame.shape[2] != 3: # 检查是否为3通道彩色图像
        return False
    if frame.shape[0] < 100 or frame.shape[1] < 100: # 检查帧尺寸是否过小
        return False
    mean_val = np.mean(frame)
    if mean_val < 10 or mean_val > 245: # 检查是否为全黑或全白帧
        return False
    return True

def draw_detections(frame, detections):
    """
    在帧上绘制YOLO检测结果的边框和标签。
    """
    for x1, y1, x2, y2, label in detections:
        # 绘制绿色边框
        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        
        # 绘制标签背景（实心绿色矩形）
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
        cv2.rectangle(frame, (x1, y1 - label_size[1] - 10), 
                      (x1 + label_size[0], y1), (0, 255, 0), -1)
        
        # 绘制标签文字（黑色）
        cv2.putText(frame, label, (x1, y1 - 5), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    
    return frame

class DynamicStreamingHandler(http.server.BaseHTTPRequestHandler):
    """
    HTTP流媒体处理器。
    通过构造函数参数 'source_type' 决定是提供 'processed' 帧还是 'raw' 帧。
    """
    def __init__(self, *args, source_type, **kwargs):
        self.source_type = source_type # 'processed' 或 'raw'
        super().__init__(*args, **kwargs)

    def do_GET(self):
        global current_processed_frame, current_raw_frame
        
        if self.path == '/':
            # 主页面 - 列出所有配置的视频流的链接，方便团队成员选择
            self.send_response(200)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.end_headers()
            
            local_ip = get_local_ip()
            stream_links_html = ""
            # 遍历Config中定义的每个视频流配置，生成HTML链接
            for stream_cfg in Config.STREAM_CONFIGS:
                stream_url = f"http://{local_ip}:{stream_cfg['port']}{stream_cfg['path']}"
                stream_home_url = f"http://{local_ip}:{stream_cfg['port']}/"
                stream_links_html += f"""
                <div class="stream-card">
                    <h3>{stream_cfg['name']}</h3>
                    <p>端口: {stream_cfg['port']}</p>
                    <p>源类型: {stream_cfg['source'].capitalize()}</p>
                    <p><a href="{stream_url}" target="_blank">点击此处查看视频流 (新窗口)</a></p>
                    <p><a href="{stream_home_url}" target="_self">返回此端口主页</a></p>
                    <img src="{stream_url}" alt="{stream_cfg['name']}" style="max-width: 90%; border-radius: 5px;">
                </div>
                """

            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>RTSP垃圾检测多路流服务器</title>
                <style>
                    body {{ font-family: 'Inter', Arial, sans-serif; text-align: center; background: #f0f2f5; color: #333; margin: 0; padding: 20px; }}
                    .container {{ max-width: 1200px; margin: 20px auto; padding: 30px; background: #ffffff; border-radius: 12px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); }}
                    h1 {{ color: #2c3e50; margin-bottom: 25px; font-size: 2.5em; }}
                    .info {{ background: #e9f7ef; color: #28a745; padding: 15px; margin: 20px 0; border-radius: 8px; border: 1px solid #d4edda; font-size: 1.1em; }}
                    .info p {{ margin: 5px 0; }}
                    h2 {{ color: #34495e; margin-top: 40px; margin-bottom: 20px; font-size: 2em; }}
                    .stream-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 30px; margin-top: 30px; }}
                    .stream-card {{ background: #fdfdfd; border: 1px solid #e0e0e0; border-radius: 10px; padding: 20px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05); text-align: left; }}
                    .stream-card h3 {{ color: #007bff; margin-top: 0; font-size: 1.5em; }}
                    .stream-card p {{ margin: 8px 0; font-size: 1em; }}
                    .stream-card a {{ color: #007bff; text-decoration: none; font-weight: bold; }}
                    .stream-card a:hover {{ text-decoration: underline; }}
                    img {{ border: 2px solid #ddd; border-radius: 8px; max-width: 100%; height: auto; display: block; margin: 15px auto 0; }}
                    .tip {{ margin-top: 30px; font-style: italic; color: #666; font-size: 0.9em; }}
                    @media (max-width: 768px) {{
                        .stream-grid {{ grid-template-columns: 1fr; }}
                        .container {{ padding: 20px; }}
                    }}
                </style>
                <script src="https://cdn.tailwindcss.com"></script>
            </head>
            <body>
                <div class="container">
                    <h1>🗑️ RTSP垃圾检测多路流服务器</h1>
                    <div class="info">
                        <p>实时检测目标: bottle (瓶子) 和 cell phone (手机)</p>
                        <p>RTSP视频源: {Config.RTSP_URL}</p>
                        <p>本地IP地址: {local_ip}</p>
                    </div>
                    <h2>可用视频流:</h2>
                    <div class="stream-grid">
                        {stream_links_html}
                    </div>
                    <p class="tip">💡 提示: 每个视频流都在一个独立的HTTP服务端口上运行。</p>
                </div>
            </body>
            </html>
            """
            self.wfile.write(html.encode())
            
        elif self.path == '/video_feed':
            # MJPEG流推送
            self.send_response(200)
            self.send_header('Content-Type', 'multipart/x-mixed-replace; boundary=frame')
            self.send_header('Cache-Control', 'no-cache')
            self.send_header('Connection', 'close') # 关闭连接以强制浏览器重新请求MJPEG
            self.end_headers()
            
            frame_count = 0
            while True:
                try:
                    frame_to_send = None
                    with frame_lock: # 锁定以安全访问全局帧变量
                        if self.source_type == "processed":
                            frame_to_send = current_processed_frame.copy() if current_processed_frame is not None else None
                        elif self.source_type == "raw":
                            frame_to_send = current_raw_frame.copy() if current_raw_frame is not None else None
                    
                    # 如果没有可用帧（例如RTSP流未连接），则推送测试帧
                    if frame_to_send is None:
                        frame_to_send = create_test_frame()
                        # 修正：使用英文，避免中文乱码问题
                        cv2.putText(frame_to_send, f"Stream Type: {self.source_type.capitalize()} | Frame: {frame_count}", (20, 60), 
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 2)
                    
                    # 将帧编码为JPEG格式
                    ret, buffer = cv2.imencode('.jpg', frame_to_send, 
                                                 [cv2.IMWRITE_JPEG_QUALITY, 85]) # 85%的JPEG质量
                    if ret:
                        frame_bytes = buffer.tobytes()
                        
                        # 构建MJPEG流的HTTP响应体
                        self.wfile.write(b'--frame\r\n')
                        self.wfile.write(b'Content-Type: image/jpeg\r\n')
                        self.wfile.write(f'Content-Length: {len(frame_bytes)}\r\n\r\n'.encode())
                        self.wfile.write(frame_bytes)
                        self.wfile.write(b'\r\n')
                        self.wfile.flush() # 立即发送数据
                        
                        frame_count += 1
                        
                    sleep(1.0 / Config.HTTP_FPS) # 根据配置控制流的帧率

                except ConnectionResetError:
                    # 客户端断开连接，这是MJPEG流的正常结束情况
                    break
                except BrokenPipeError:
                    # 管道断开，也是客户端断开的正常情况
                    break
                except Exception as e:
                    # 捕获并打印其他异常，避免频繁的连接中断错误信息
                    if "connection" not in str(e).lower() and "winerror 10038" not in str(e).lower(): # 10038是Windows上典型的套接字关闭错误
                        print(f"[HTTP] 流传输错误 ({self.source_type}): {e}")
                    break
        else:
            # 处理未知的路径请求
            self.send_response(404)
            self.end_headers()

    def log_message(self, format, *args):
        # 抑制HTTP服务器的访问日志，保持控制台输出简洁
        _ = format, args
        pass

def process_frames():
    """
    独立的线程函数，负责从队列获取帧，执行YOLO检测，
    并更新全局的 'processed' 帧。
    """
    global current_processed_frame

    while True:
        try:
            # 从队列中获取待处理的帧，设置超时时间
            frame = frame_queue.get(timeout=5)

            if not is_frame_valid(frame):
                stats['error_frames'] += 1
                continue

            stats['valid_frames'] += 1

            # YOLO检测（使用配置的设备，imgsz=416可提供较快的推理速度）
            results = model(frame, imgsz=416, conf=CONFIDENCE_THRESHOLD, verbose=False, device=device)[0]
            detections = []

            # 解析YOLO检测结果
            for box in results.boxes:
                class_id = int(box.cls)
                if class_id in garbage_classes: # 只关心配置的垃圾类别
                    x1, y1, x2, y2 = map(int, box.xyxy[0]) # 边界框坐标
                    confidence = float(box.conf) # 置信度
                    label = f"{garbage_classes[class_id]} {confidence:.2f}" # 标签文本
                    detections.append((x1, y1, x2, y2, label))

            # 在帧上绘制检测结果
            processed_frame = draw_detections(frame.copy(), detections)

            # 添加信息文字到帧上
            # 修正：使用英文，避免中文乱码问题
            cv2.putText(processed_frame, f"Objects: {len(detections)} | Device: {device}",
                        (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            cv2.putText(processed_frame, f"IP: {get_local_ip()}",
                        (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

            # 更新全局的processed帧，并加锁保护
            with frame_lock:
                current_processed_frame = processed_frame

            stats['frames_processed'] += 1

        except Empty:
            print("[WARNING] 帧处理线程等待队列超时，无帧可处理。")
            continue
        except Exception as e:
            print(f"[PROCESS_THREAD] 帧处理线程异常: {e}")
            break

    print("[PROCESS_THREAD] 帧处理线程退出。")

def start_http_server(port, handler_class, server_name="HTTP Server"):
    """
    启动HTTP流媒体服务器的独立函数。
    每个HTTP服务器都在一个单独的线程中运行。
    """
    try:
        from http.server import HTTPServer
        # 绑定到所有网络接口 (0.0.0.0)，以便局域网内的其他设备可以访问
        httpd = HTTPServer(("0.0.0.0", port), handler_class)
        local_ip = get_local_ip()
        print(f"[{server_name}] 服务器在端口 {port} 启动...")
        print(f"[{server_name}] 本地访问: http://localhost:{port}/")
        print(f"[{server_name}] 局域网访问: http://{local_ip}:{port}/")
        print(f"[{server_name}] 直接视频流URL: http://{local_ip}:{port}/video_feed")

        # 设置服务器超时，防止在没有请求时无限阻塞
        httpd.timeout = 1.0

        # 启动服务器请求处理循环
        print(f"[{server_name}] 服务器开始监听...")
        while True:
            try:
                httpd.handle_request() # 处理单个HTTP请求
            except KeyboardInterrupt:
                print(f"\n[{server_name}] 收到退出信号，服务器停止。")
                break
            except Exception as e:
                # 抑制常见的连接中断错误，只打印其他关键错误
                if "connection" not in str(e).lower() and "winerror 10038" not in str(e).lower():
                    print(f"[{server_name}] 请求处理错误: {e}")
                continue # 继续监听下一个请求

    except Exception as e:
        print(f"[{server_name}] 服务器启动失败: {e}")
        import traceback
        traceback.print_exc() # 打印完整的异常堆栈信息

def print_stats():
    """
    定期打印程序的运行统计信息，包括帧率、内存使用等。
    """
    elapsed = time() - stats['start_time']
    fps_received = stats['frames_received'] / elapsed if elapsed > 0 else 0
    fps_processed = stats['frames_processed'] / elapsed if elapsed > 0 else 0
    fps_displayed = stats['frames_displayed'] / elapsed if elapsed > 0 else 0
    valid_rate = (stats['valid_frames'] / max(stats['frames_received'], 1)) * 100

    print("\n" + "=" * 60)
    print("📊 运行统计")
    print("=" * 60)
    print(f"运行时间: {elapsed:.1f}秒")
    print(f"设备: {device}")

    # 打印GPU内存使用情况（如果使用CUDA）
    if device == 'cuda' and torch.cuda.is_available():
        gpu_memory_used = torch.cuda.memory_allocated(0) / 1024**3
        gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / 1024**3
        gpu_utilization = (gpu_memory_used / gpu_memory_total) * 100
        print(f"GPU内存: {gpu_memory_used:.2f}GB / {gpu_memory_total:.1f}GB ({gpu_utilization:.1f}%)")

    print(f"收到帧数: {stats['frames_received']} ({fps_received:.1f} FPS)")
    print(f"有效帧数: {stats['valid_frames']} (有效率: {valid_rate:.1f}%)")
    print(f"处理帧数: {stats['frames_processed']} ({fps_processed:.1f} FPS)")
    print(f"显示帧数: {stats['frames_displayed']} ({fps_displayed:.1f} FPS)")
    print(f"队列丢弃: {stats['queue_drops']}")
    print("=" * 60 + "\n")

def main():
    """
    主函数，负责初始化、启动线程、处理RTSP流以及管理整个程序的生命周期。
    """
    global current_raw_frame

    # 获取本机IP地址并打印
    local_ip = get_local_ip()
    print(f"[INFO] 本机IP地址: {local_ip}")

    # 启动帧处理线程 (daemon=True 确保主程序退出时线程自动终止)
    Thread(target=process_frames, daemon=True).start()
    print("[THREAD] 帧处理线程已启动。")

    # 启动HTTP流媒体服务器（每个配置的流一个独立线程）
    http_server_threads = []
    for stream_cfg in Config.STREAM_CONFIGS:
        # 使用functools.partial创建HTTP处理程序，将source_type参数绑定到DynamicStreamingHandler
        handler_with_args = partial(DynamicStreamingHandler, source_type=stream_cfg['source'])
        
        http_thread = Thread(target=start_http_server, 
                             args=(stream_cfg['port'], handler_with_args, stream_cfg['name']))
        http_thread.daemon = True # 设为守护线程，随主程序退出
        http_server_threads.append(http_thread)
        http_thread.start()
        sleep(0.5) # 稍微延迟，确保服务器启动顺序

    print(f"[MAIN] 启动RTSP流垃圾检测应用。")
    print(f"[MAIN] RTSP视频源: {Config.RTSP_URL}")
    print(f"[MAIN] 🚀 运行设备: {device}")
    if device == 'cuda':
        print(f"[MAIN] 🎮 GPU加速: 已启用")
    else:
        print(f"[MAIN] 💻 CPU模式: 已启用")
    print(f"[MAIN] 💡 如果启用了OpenCV显示窗口，可按 'q' 退出，按 's' 保存截图。")
    print(f"[MAIN] 🌐 可用的HTTP视频流地址:")
    for stream_cfg in Config.STREAM_CONFIGS:
        print(f"    - {stream_cfg['name']}: http://{local_ip}:{stream_cfg['port']}/")
    print(f"[MAIN] � 局域网访问: 在浏览器中打开上述任意地址即可。")

    # 主循环变量
    fps_counter = 0
    fps_timer = time()
    last_stats_time = time()

    try:
        # 打开RTSP流
        print(f"[RTSP] 尝试连接RTSP流: {Config.RTSP_URL}")
        cap = cv2.VideoCapture(Config.RTSP_URL)

        # 检查RTSP流是否成功打开
        if not cap.isOpened():
            print(f"[ERROR] 无法打开RTSP流: {Config.RTSP_URL}")
            print("[INFO] 将在测试模式下运行HTTP服务器（显示测试图像）。")
            # 即使RTSP流失败，也要保持HTTP服务器运行，以便提供测试帧
            try:
                while True:
                    sleep(1) # 暂停1秒，避免CPU空转
                    # 定期打印统计信息
                    if time() - last_stats_time > STATS_INTERVAL:
                        print_stats()
                        last_stats_time = time()
            except KeyboardInterrupt:
                print("\n[MAIN] 收到退出信号，程序退出。")
            return # 退出主函数

        print(f"[RTSP] RTSP流连接成功。")

        # 优化RTSP设置
        cap.set(cv2.CAP_PROP_BUFFERSIZE, Config.RTSP_BUFFER_SIZE) # 最小化缓冲区以降低延迟
        # cap.set(cv2.CAP_PROP_FPS, 30) # 尝试设置帧率，但摄像头不一定支持

        # 连接重试计数器
        reconnect_count = 0
        max_reconnects = Config.RTSP_RECONNECT_MAX

        # 主循环：读取RTSP帧，放入队列，并更新全局原始帧
        while True:
            ret, frame = cap.read()

            if not ret: # 如果无法读取到帧，则尝试重连
                reconnect_count += 1
                print(f"[WARNING] 无法读取RTSP帧，尝试重连... ({reconnect_count}/{max_reconnects})")

                cap.release() # 释放当前捕获对象
                sleep(min(reconnect_count * 2, 10)) # 递增延迟，最大10秒

                cap = cv2.VideoCapture(Config.RTSP_URL) # 重新创建捕获对象
                if not cap.isOpened():
                    if reconnect_count >= max_reconnects:
                        print(f"[ERROR] RTSP重连失败，已达到最大重试次数 ({max_reconnects})。退出程序。")
                        break # 达到最大重试次数，退出主循环
                    continue # 继续下一次重试
                else:
                    print("[INFO] RTSP重连成功。")
                    reconnect_count = 0 # 重置计数器
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, Config.RTSP_BUFFER_SIZE) # 重新设置缓冲区
                continue

            # 如果成功读取帧，重置重连计数
            if reconnect_count > 0:
                reconnect_count = 0

            stats['frames_received'] += 1

            # 更新全局原始帧 (使用锁保护)
            with frame_lock:
                current_raw_frame = frame.copy() # 存储一份副本供原始流使用

            # 智能队列管理：将帧放入处理队列
            try:
                frame_queue.put_nowait(frame.copy()) # 放入帧的副本供YOLO处理
            except:
                # 如果队列已满，则清空队列并放入新帧，以确保总是处理最新帧
                try:
                    while not frame_queue.empty():
                        frame_queue.get_nowait()
                        stats['queue_drops'] += 1 # 记录丢弃的帧数
                    frame_queue.put_nowait(frame.copy())
                except Exception as e:
                    print(f"[WARNING] 队列管理错误: {e}")

            # 可选的OpenCV窗口显示 (根据Config.ENABLE_DISPLAY)
            try:
                if Config.ENABLE_DISPLAY:
                    display_frame = None
                    with frame_lock:
                        if current_processed_frame is not None:
                            display_frame = current_processed_frame.copy()
                        elif current_raw_frame is not None:
                            # 如果处理后的帧暂不可用，则回退显示原始帧
                            display_frame = current_raw_frame.copy()

                    if display_frame is not None:
                        cv2.imshow("RTSP Garbage Detection", display_frame)
                        stats['frames_displayed'] += 1

                        # 处理按键事件
                        key = cv2.waitKey(1) & 0xFF
                        if key == ord('q'):
                            print("[MAIN] 用户请求退出。")
                            break # 用户按'q'键退出
                        elif key == ord('s'):
                            # 按's'键保存截图
                            if current_processed_frame is not None:
                                filename = f"screenshot_processed_{int(time())}.jpg"
                                cv2.imwrite(filename, current_processed_frame)
                                print(f"[MAIN] 处理后的截图已保存: {filename}")
                            elif current_raw_frame is not None:
                                filename = f"screenshot_raw_{int(time())}.jpg"
                                cv2.imwrite(filename, current_raw_frame)
                                print(f"[MAIN] 原始截图已保存: {filename}")
                else:
                    # 在无图形界面环境下，通过sleep减少CPU占用
                    sleep(0.01)
            except Exception as e:
                # 捕获显示相关的错误，通常不影响核心功能
                if "cannot connect to X server" not in str(e).lower() and "noatime" not in str(e).lower():
                    print(f"[WARNING] 显示错误: {e}")
                sleep(0.01) # 即使有错误也小幅暂停，避免无限循环占用CPU

            # FPS计算
            fps_counter += 1
            if time() - fps_timer > 1.0:
                fps_timer = time()
                fps_counter = 0

            # 定期打印统计信息
            if time() - last_stats_time > STATS_INTERVAL:
                print_stats()
                last_stats_time = time()

    except KeyboardInterrupt:
        print("\n[MAIN] 收到退出信号，程序正在关闭...")
    except Exception as e:
        print(f"[ERROR] 主循环异常: {e}")
        import traceback
        traceback.print_exc() # 打印主循环中的异常堆栈
    finally:
        # 清理资源
        try:
            if 'cap' in locals() and cap.isOpened():
                cap.release() # 释放视频捕获对象
        except:
            pass
        cv2.destroyAllWindows() # 关闭所有OpenCV窗口

        # 最终打印统计信息
        print_stats()
        print("[MAIN] 程序已退出。")

if __name__ == "__main__":
    main()
