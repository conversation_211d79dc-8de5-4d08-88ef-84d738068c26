#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
main_v12.py - RTSP垃圾检测系统 (多线程HTTP服务器版)
基于main_v11.py，新增功能：
1. 使用多线程HTTP服务器解决视频流阻塞API请求的问题
2. 视频流和API请求可以并发处理
3. 保持所有原有功能不变
"""

import os
import cv2
import numpy as np
import http.server
import socketserver
import json
import threading
import time
import socket
import signal
import sys
from threading import Lock
from queue import Queue, Empty
from ultralytics import YOLO
import torch

# 环境配置 - 抑制各种日志输出
os.environ['OPENCV_LOG_LEVEL'] = 'SILENT'
os.environ['OPENCV_VIDEOIO_DEBUG'] = '0'
os.environ['OPENCV_FFMPEG_LOGLEVEL'] = '-8'
os.environ['FFMPEG_HIDE_BANNER'] = '1'
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['YOLO_VERBOSE'] = 'False'

import warnings
warnings.filterwarnings('ignore')
cv2.setLogLevel(0)

# 多线程HTTP服务器类
class ThreadedHTTPServer(socketserver.ThreadingMixIn, socketserver.TCPServer):
    """多线程HTTP服务器 - 解决视频流阻塞API请求的问题"""
    allow_reuse_address = True  # 允许端口重用
    daemon_threads = True  # 守护线程，主程序退出时自动结束

# 配置类
class Config:
    # RTSP流配置
    RTSP_URL = "rtsp://*************:8554/camera"
    RTSP_BUFFER_SIZE = 1
    
    # 检测配置
    CONFIDENCE_THRESHOLD = 0.5
    MODEL_PATH = 'yolov8n.pt'
    
    # HTTP配置
    HTTP_PORT_START = 5000  # 起始端口号
    HTTP_PORT_MAX = 5010    # 最大端口号
    
    # 性能配置
    QUEUE_SIZE = 3

# 全局变量
current_raw_frame = None  # 原始视频帧
current_processed_frame = None  # 处理后的视频帧
frame_lock = Lock()  # 帧访问锁，防止多线程冲突
frame_queue = Queue(maxsize=Config.QUEUE_SIZE)  # 帧处理队列
httpd_server = None  # HTTP服务器实例，用于程序结束时释放端口

# API控制变量
detection_enabled = False  # 检测是否启用
processing_active = False  # 处理是否活跃

# 统计信息
stats = {
    'frames_received': 0,
    'frames_processed': 0,
    'valid_frames': 0,
    'error_frames': 0,
    'queue_drops': 0,
    'detections_total': 0,
    'detections_current': 0,
    'model_inference_time': 0,
    'current_objects': [],  # 当前检测到的物品列表
    'start_time': time.time()
}

# 系统信息
system_info = {
    'device': 'cpu',
    'model_loaded': False,
    'model_path': Config.MODEL_PATH,
    'rtsp_url': Config.RTSP_URL,
    'rtsp_connected': False,
    'rtsp_latency': 0,  # RTSP通信延迟时间(毫秒)
    'python_version': '',
    'opencv_version': '',
    'torch_version': '',
    'local_ip': '',
    'yolo_classes': [],
    'http_port': 0,  # 实际使用的HTTP端口
}

# 设备检测和模型初始化
device = 'cuda' if torch.cuda.is_available() else 'cpu'
system_info['device'] = device
model = None

def signal_handler(sig, frame):
    """信号处理器 - 程序结束时释放端口"""
    print("\n[CLEANUP] 接收到退出信号，正在清理资源...")
    cleanup_and_exit()

def cleanup_and_exit():
    """清理资源并退出程序"""
    global httpd_server
    
    print("[CLEANUP] 正在释放HTTP服务器端口...")
    if httpd_server:
        try:
            httpd_server.shutdown()  # 关闭服务器
            httpd_server.server_close()  # 释放端口
            print(f"[CLEANUP] 端口 {system_info['http_port']} 已释放")
        except Exception as e:
            print(f"[CLEANUP] 释放端口时出错: {e}")
    
    print("[CLEANUP] 程序退出")
    sys.exit(0)

def find_available_port(start_port=Config.HTTP_PORT_START, max_port=Config.HTTP_PORT_MAX):
    """寻找可用端口 - 从起始端口开始，逐个+1测试直到找到可用端口"""
    for port in range(start_port, max_port + 1):
        try:
            # 尝试绑定端口测试是否可用
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            test_socket.bind(('', port))
            test_socket.close()
            print(f"[PORT] 找到可用端口: {port}")
            return port
        except OSError:
            print(f"[PORT] 端口 {port} 被占用，尝试下一个...")
            continue
    
    # 如果所有端口都被占用
    raise Exception(f"[ERROR] 端口范围 {start_port}-{max_port} 内没有可用端口")

def get_local_ip():
    """获取本地IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def initialize_system():
    """初始化系统信息"""
    global system_info
    
    system_info['local_ip'] = get_local_ip()
    system_info['python_version'] = f"{torch.__version__}"
    system_info['opencv_version'] = cv2.__version__
    system_info['torch_version'] = torch.__version__
    
    print(f"[INIT] 系统初始化完成")
    print(f"[INIT] 设备: {device}")
    print(f"[INIT] 本地IP: {system_info['local_ip']}")
    print(f"[INIT] OpenCV版本: {system_info['opencv_version']}")
    print(f"[INIT] PyTorch版本: {system_info['torch_version']}")

def load_model():
    """延迟加载YOLO模型"""
    global model, system_info
    
    if model is None:
        try:
            print(f"[MODEL] 加载YOLO模型: {Config.MODEL_PATH}")
            model = YOLO(Config.MODEL_PATH)

            # 强制移动到指定设备
            if device == 'cuda':
                model.to(device)
                print(f"[MODEL] 模型已移动到CUDA设备")

            # 获取模型类别信息
            system_info['yolo_classes'] = list(model.names.values())
            system_info['model_loaded'] = True

            print(f"[MODEL] 模型加载成功，设备: {device}")
            print(f"[MODEL] 支持类别数: {len(system_info['yolo_classes'])}")

            # 预热模型
            dummy_frame = np.zeros((640, 640, 3), dtype=np.uint8)
            _ = model(dummy_frame, verbose=False)
            print(f"[MODEL] 模型预热完成")

            return True
        except Exception as e:
            print(f"[ERROR] 模型加载失败: {e}")
            system_info['model_loaded'] = False
            return False
    else:
        # 模型已经加载，确保状态正确
        system_info['model_loaded'] = True
        return True

def is_frame_valid(frame):
    """检查帧是否有效"""
    if frame is None or frame.size == 0:
        return False
    if len(frame.shape) < 2:
        return False
    return True

def draw_detections(frame, results):
    """在帧上绘制检测结果并收集当前检测到的物品"""
    detections = 0
    current_objects = []
    
    if hasattr(results, 'boxes') and results.boxes is not None:
        boxes = results.boxes
        detections = len(boxes)
        
        for box in boxes:
            # 获取边界框坐标和类别信息
            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
            confidence = box.conf[0].cpu().numpy()
            class_id = int(box.cls[0].cpu().numpy())
            
            # 获取类别名称
            class_name = model.names[class_id] if class_id < len(model.names) else f"Class_{class_id}"
            current_objects.append(class_name)
            
            # 绘制边界框
            cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
            
            # 绘制标签
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            cv2.rectangle(frame, (int(x1), int(y1) - label_size[1] - 10), 
                         (int(x1) + label_size[0], int(y1)), (0, 255, 0), -1)
            cv2.putText(frame, label, (int(x1), int(y1) - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    
    # 更新全局当前物品列表
    stats['current_objects'] = current_objects
    
    return detections

def process_frames():
    """帧处理线程 - 负责YOLO检测和结果绘制"""
    global current_processed_frame, detection_enabled, processing_active
    
    print("[THREAD] 帧处理线程启动")
    
    while True:
        try:
            # 从队列获取待处理帧
            frame = frame_queue.get(timeout=2)
            
            if is_frame_valid(frame):
                stats['valid_frames'] += 1
                processed_frame = frame.copy()
                
                # 只有在检测启用时才进行YOLO处理
                if detection_enabled:
                    # 确保模型已加载
                    if not load_model():
                        cv2.putText(processed_frame, "Model Loading Failed", (10, 30), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                        stats['detections_current'] = 0
                        stats['current_objects'] = []
                    else:
                        try:
                            # 记录推理开始时间
                            inference_start = time.time()
                            
                            # YOLO检测
                            results = model(frame, imgsz=640, conf=Config.CONFIDENCE_THRESHOLD, 
                                          verbose=False, device=device)[0]
                            
                            # 记录推理时间
                            inference_time = time.time() - inference_start
                            stats['model_inference_time'] = inference_time
                            
                            # 绘制检测结果
                            detections = draw_detections(processed_frame, results)
                            stats['detections_current'] = detections
                            stats['detections_total'] += detections
                            
                            # 添加检测信息到帧上
                            info_text = f"Objects: {detections} | Device: {device} | Time: {inference_time*1000:.1f}ms"
                            cv2.putText(processed_frame, info_text, (10, 30), 
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                            
                            # 添加端口信息
                            port_text = f"Port: {system_info['http_port']}"
                            cv2.putText(processed_frame, port_text, (10, 60),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
                            
                            stats['frames_processed'] += 1
                                
                        except Exception as e:
                            print(f"[ERROR] YOLO检测错误: {e}")
                            cv2.putText(processed_frame, f"Detection Error: {str(e)[:30]}", (10, 30), 
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
                            stats['detections_current'] = 0
                            stats['current_objects'] = []
                else:
                    # 检测禁用状态
                    cv2.putText(processed_frame, "Detection DISABLED - Use API to enable", 
                               (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                    cv2.putText(processed_frame, f"Port: {system_info['http_port']}",
                               (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
                    stats['detections_current'] = 0
                    stats['current_objects'] = []
                
                # 更新全局处理帧
                with frame_lock:
                    current_processed_frame = processed_frame
            else:
                stats['error_frames'] += 1
                
        except Empty:
            continue
        except Exception as e:
            print(f"[ERROR] 帧处理错误: {e}")
            stats['error_frames'] += 1

class WebHandler(http.server.BaseHTTPRequestHandler):
    """Web界面处理器 - 支持多线程并发处理"""

    def do_GET(self):
        """处理GET请求 - 在独立线程中运行"""
        if self.path == '/':
            self.send_main_page()
        elif self.path == '/video_feed':
            self.send_video_feed()
        else:
            self.send_error(404)

    def do_POST(self):
        """处理POST请求 - API调用在独立线程中运行，不会被视频流阻塞"""
        global detection_enabled, processing_active

        # 记录请求处理的线程ID，用于调试
        thread_id = threading.current_thread().ident

        if self.path == '/api/start':
            # 启动检测API
            detection_enabled = True
            processing_active = True
            load_model()  # 预加载模型

            # 仅返回HTTP视频流地址
            video_url = f"http://{system_info['local_ip']}:{system_info['http_port']}/video_feed"
            response = {
                "status": "success",
                "message": "Detection started",
                "video_url": video_url,
                "thread_id": thread_id,  # 新增：显示处理线程ID
            }
            print(f"[API] ✅ 检测启动 - 线程ID: {thread_id} - 返回视频流地址: {video_url}")

        elif self.path == '/api/stop':
            # 停止检测API
            detection_enabled = False
            processing_active = False
            response = {
                "status": "success",
                "message": "Detection stopped",
                "thread_id": thread_id,
            }
            print(f"[API] 🛑 检测停止 - 线程ID: {thread_id}")

        elif self.path == '/api/status':
            # 状态查询API
            elapsed = time.time() - stats['start_time']
            fps_received = stats['frames_received'] / elapsed if elapsed > 0 else 0
            # 处理FPS只在检测启用时才计算
            fps_processed = (stats['frames_processed'] / elapsed if elapsed > 0 else 0) if detection_enabled else 0

            response = {
                "status": "success",
                "detection_enabled": detection_enabled,
                "processing_active": processing_active,
                "stats": {
                    **stats,
                    "elapsed_time": elapsed,
                    "fps_received": fps_received,
                    "fps_processed": fps_processed
                },
                "system_info": system_info,
                "thread_id": thread_id,
            }
        else:
            response = {
                "status": "error",
                "message": "Unknown API endpoint",
                "thread_id": thread_id
            }

        # 发送JSON响应
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def send_main_page(self):
        """发送主页面 - 增加多线程服务器说明"""
        local_ip = system_info['local_ip']
        http_port = system_info['http_port']

        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>检测控制台</title>
            <style>
                body {{ font-family: Arial, sans-serif; background: #f0f2f5; color: #333; margin: 0; padding: 20px; }}
                .container {{ max-width: 1200px; margin: 0 auto; }}
                h1 {{ text-align: center; color: #2c3e50; margin-bottom: 30px; }}
                .main-content {{ display: flex; gap: 20px; }}
                .left-panel, .right-panel {{ flex: 1; background: #ffffff; padding: 20px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); }}
                .panel-title {{ font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 5px; }}
                .info-item {{ margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 5px; }}
                .info-label {{ font-weight: bold; color: #555; }}
                .info-value {{ color: #333; }}
                .controls {{ text-align: center; margin: 30px 0; }}
                .status {{ padding: 15px; margin: 20px 0; border-radius: 8px; font-weight: bold; text-align: center; }}
                .btn {{ padding: 12px 24px; margin: 8px; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: bold; transition: all 0.3s; }}
                .btn-start {{ background: #28a745; color: white; }}
                .btn-start:hover {{ background: #218838; }}
                .btn-stop {{ background: #dc3545; color: white; }}
                .btn-stop:hover {{ background: #c82333; }}
                .btn-refresh {{ background: #007bff; color: white; }}
                .btn-refresh:hover {{ background: #0056b3; }}
                .btn-video {{ background: #17a2b8; color: white; }}
                .btn-video:hover {{ background: #138496; }}
                .stats-grid {{ display: grid; grid-template-columns: 1fr 1fr; gap: 10px; }}
                .detection-highlight {{ background: #e8f5e8; border-left: 4px solid #28a745; }}
                .current-objects {{ background: #fff3cd; border-left: 4px solid #ffc107; padding: 10px; margin-top: 10px; border-radius: 5px; }}
                .port-info {{ background: #d1ecf1; border-left: 4px solid #17a2b8; padding: 10px; margin: 10px 0; border-radius: 5px; }}
                .threading-info {{ background: #d4edda; border-left: 4px solid #28a745; padding: 10px; margin: 10px 0; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>检测控制台</h1>



                <div class="port-info">
                    <strong>🌐 服务信息:</strong>
                    当前端口: {http_port} |
                    视频流: <a href="http://{local_ip}:{http_port}/video_feed" target="_blank">http://{local_ip}:{http_port}/video_feed</a>
                </div>

                <div class="status" id="status">检测状态: 加载中...</div>

                <div class="controls">
                    <button class="btn btn-start" onclick="startDetection()">🚀 启动YOLO检测</button>
                    <button class="btn btn-stop" onclick="stopDetection()">🛑 停止检测</button>
                    <button class="btn btn-video" onclick="openVideoFeed()">📺 打开Web端视频</button>
                    <button class="btn btn-refresh" onclick="updateStatus()">🔄 刷新状态</button>
                </div>

                <div class="main-content">
                    <div class="left-panel">
                        <div class="panel-title">📊 系统信息</div>
                        <div id="system-info">
                            <div class="info-item">
                                <span class="info-label">本地IP:</span>
                                <span class="info-value" id="local-ip">{local_ip}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">HTTP端口:</span>
                                <span class="info-value" id="http-port">{http_port}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">计算设备:</span>
                                <span class="info-value" id="device">检测中...</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">模型状态:</span>
                                <span class="info-value" id="model-status">检测中...</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">RTSP源:</span>
                                <span class="info-value" id="rtsp-url">检测中...</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">通信延迟:</span>
                                <span class="info-value" id="rtsp-latency">检测中...</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">支持类别:</span>
                                <span class="info-value" id="yolo-classes">检测中...</span>
                            </div>
                        </div>
                    </div>

                    <div class="right-panel">
                        <div class="panel-title">📈 实时检测信息</div>
                        <div id="detection-info">
                            <div class="stats-grid">
                                <div class="info-item">
                                    <span class="info-label">运行时间:</span>
                                    <span class="info-value" id="elapsed-time">0秒</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">接收帧数:</span>
                                    <span class="info-value" id="frames-received">0</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">处理帧数:</span>
                                    <span class="info-value" id="frames-processed">0</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">有效帧数:</span>
                                    <span class="info-value" id="valid-frames">0</span>
                                </div>
                                <div class="info-item detection-highlight">
                                    <span class="info-label">当前检测数:</span>
                                    <span class="info-value" id="detections-current">0</span>
                                </div>
                                <div class="info-item detection-highlight">
                                    <span class="info-label">总检测数:</span>
                                    <span class="info-value" id="detections-total">0</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">推理时间:</span>
                                    <span class="info-value" id="inference-time">0ms</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">处理FPS:</span>
                                    <span class="info-value" id="fps-processed">0.0</span>
                                </div>
                            </div>
                            <div class="current-objects" id="current-objects-panel">
                                <div class="info-label">当前检测物品:</div>
                                <div class="info-value" id="current-objects">无</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <script>
                function updateStatus() {{
                    fetch('/api/status', {{method: 'POST'}})
                        .then(response => response.json())
                        .then(data => {{
                            // 更新检测状态显示
                            const statusDiv = document.getElementById('status');
                            if (data.detection_enabled) {{
                                statusDiv.style.background = '#28a745';
                                statusDiv.style.color = 'white';
                                statusDiv.innerHTML = '检测状态: <strong>🟢 YOLO检测启用中</strong>';
                            }} else {{
                                statusDiv.style.background = '#dc3545';
                                statusDiv.style.color = 'white';
                                statusDiv.innerHTML = '检测状态: <strong>🔴 检测禁用</strong>';
                            }}

                            // 更新系统信息
                            const sysInfo = data.system_info;
                            document.getElementById('device').textContent = sysInfo.device.toUpperCase() + (sysInfo.device === 'cuda' ? ' (GPU加速)' : ' (CPU模式)');
                            document.getElementById('model-status').textContent = sysInfo.model_loaded ? '✅ 已加载' : '❌ 未加载';
                            document.getElementById('rtsp-url').textContent = sysInfo.rtsp_url;
                            document.getElementById('rtsp-latency').textContent = sysInfo.rtsp_connected ? sysInfo.rtsp_latency + 'ms' : '未连接';
                            document.getElementById('yolo-classes').textContent = sysInfo.yolo_classes.length + ' 个类别';

                            // 更新检测统计信息
                            const stats = data.stats;
                            document.getElementById('elapsed-time').textContent = Math.floor(stats.elapsed_time) + '秒';
                            document.getElementById('frames-received').textContent = stats.frames_received;
                            document.getElementById('frames-processed').textContent = stats.frames_processed;
                            document.getElementById('valid-frames').textContent = stats.valid_frames;
                            document.getElementById('detections-current').textContent = stats.detections_current || 0;
                            document.getElementById('detections-total').textContent = stats.detections_total || 0;
                            document.getElementById('inference-time').textContent = (stats.model_inference_time * 1000).toFixed(1) + 'ms';
                            document.getElementById('fps-processed').textContent = stats.fps_processed.toFixed(1);

                            // 更新当前检测物品信息
                            const currentObjects = stats.current_objects || [];
                            const objectsDiv = document.getElementById('current-objects');
                            if (currentObjects.length === 0) {{
                                objectsDiv.textContent = '无';
                                objectsDiv.style.color = '#666';
                            }} else {{
                                let displayText = '';
                                if (currentObjects.length <= 2) {{
                                    // 两个以内全显示
                                    displayText = currentObjects.join(', ');
                                }} else {{
                                    // 超过两个显示两个，后面写个"等"
                                    displayText = currentObjects.slice(0, 2).join(', ') + ' 等';
                                }}
                                objectsDiv.textContent = displayText;
                                objectsDiv.style.color = '#28a745';
                                objectsDiv.style.fontWeight = 'bold';
                            }}


                        }})
                        .catch(error => {{
                            console.error('状态更新失败:', error);
                            document.getElementById('status').innerHTML = '检测状态: <strong>❌ 更新失败</strong>';
                        }});
                }}

                function startDetection() {{
                    fetch('/api/start', {{method: 'POST'}})
                        .then(response => response.json())
                        .then(data => {{
                            if (data.status === 'success') {{
                                // 显示视频流地址
                                alert('✅ ' + data.message + '\\n\\n📺 视频流地址:\\n' + data.video_url);
                            }} else {{
                                alert('❌ 启动失败: ' + data.message);
                            }}
                            updateStatus();
                        }})
                        .catch(error => {{
                            alert('❌ 启动失败: ' + error);
                        }});
                }}

                function stopDetection() {{
                    fetch('/api/stop', {{method: 'POST'}})
                        .then(response => response.json())
                        .then(data => {{
                            alert('✅ ' + data.message);
                            updateStatus();
                        }})
                        .catch(error => {{
                            alert('❌ 停止失败: ' + error);
                        }});
                }}

                function openVideoFeed() {{
                    // 打开Web端视频流
                    const videoUrl = 'http://{local_ip}:{http_port}/video_feed';
                    window.open(videoUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
                }}

                // 页面加载时立即更新状态
                document.addEventListener('DOMContentLoaded', function() {{
                    updateStatus();
                    // 每2秒自动更新状态
                    setInterval(updateStatus, 2000);
                }});
            </script>
        </body>
        </html>
        """

        self.send_response(200)
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode())

    def send_video_feed(self):
        """发送视频流 - 在独立线程中运行，不会阻塞其他请求"""
        thread_id = threading.current_thread().ident
        print(f"[VIDEO] 视频流开始 - 线程ID: {thread_id}")

        self.send_response(200)
        self.send_header('Content-Type', 'multipart/x-mixed-replace; boundary=frame')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()

        try:
            frame_count = 0
            while True:
                frame_to_send = None

                # 获取当前处理帧
                with frame_lock:
                    if current_processed_frame is not None:
                        frame_to_send = current_processed_frame.copy()
                    elif current_raw_frame is not None:
                        frame_to_send = current_raw_frame.copy()

                if frame_to_send is not None:
                    # 编码为JPEG
                    ret, jpeg = cv2.imencode('.jpg', frame_to_send, [cv2.IMWRITE_JPEG_QUALITY, 85])

                    if ret:
                        # 发送MJPEG帧
                        self.wfile.write(b'--frame\r\n')
                        self.wfile.write(b'Content-Type: image/jpeg\r\n\r\n')
                        self.wfile.write(jpeg.tobytes())
                        self.wfile.write(b'\r\n')
                        frame_count += 1
                else:
                    # 发送等待帧
                    info_frame = np.zeros((480, 640, 3), dtype=np.uint8)
                    cv2.putText(info_frame, "Detection System v12", (150, 200),
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                    cv2.putText(info_frame, "Waiting for video signal...", (180, 240),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (128, 128, 128), 2)
                    cv2.putText(info_frame, f"Device: {device.upper()}", (220, 280),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
                    cv2.putText(info_frame, f"Port: {system_info['http_port']}", (250, 310),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

                    ret, jpeg = cv2.imencode('.jpg', info_frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                    if ret:
                        self.wfile.write(b'--frame\r\n')
                        self.wfile.write(b'Content-Type: image/jpeg\r\n\r\n')
                        self.wfile.write(jpeg.tobytes())
                        self.wfile.write(b'\r\n')

                time.sleep(1.0 / 15)  # 15 FPS

        except Exception as e:
            print(f"[VIDEO] 视频流结束 - 线程ID: {thread_id}, 发送帧数: {frame_count}, 错误: {e}")

    def log_message(self, fmt, *args):
        """抑制HTTP日志输出"""
        pass

def rtsp_capture_thread():
    """RTSP流获取线程 - 负责从RTSP源获取视频帧"""
    global current_raw_frame, processing_active, system_info

    cap = None
    reconnect_count = 0
    max_reconnects = 3

    print("[THREAD] RTSP获取线程启动")

    while True:
        try:
            # 尝试连接RTSP流
            if cap is None or not cap.isOpened():
                print(f"[RTSP] 尝试连接: {Config.RTSP_URL}")

                # 设置日志抑制
                cv2.setLogLevel(0)
                os.environ['OPENCV_FFMPEG_LOGLEVEL'] = '-8'

                cap = cv2.VideoCapture(Config.RTSP_URL)
                cap.set(cv2.CAP_PROP_BUFFERSIZE, Config.RTSP_BUFFER_SIZE)

                if not cap.isOpened():
                    reconnect_count += 1
                    print(f"[WARNING] RTSP连接失败，重试中... ({reconnect_count}/{max_reconnects})")
                    system_info['rtsp_connected'] = False

                    if reconnect_count >= max_reconnects:
                        print("[INFO] RTSP连接失败，使用测试模式")
                        # 使用测试帧模式
                        while True:
                            test_frame = create_test_frame()

                            with frame_lock:
                                current_raw_frame = test_frame.copy()

                            # 放入处理队列
                            try:
                                frame_queue.put_nowait(test_frame.copy())
                            except:
                                # 队列满，清空并放入新帧
                                try:
                                    while not frame_queue.empty():
                                        frame_queue.get_nowait()
                                        stats['queue_drops'] += 1
                                    frame_queue.put_nowait(test_frame.copy())
                                except:
                                    pass

                            stats['frames_received'] += 1
                            time.sleep(1.0 / 10)  # 10 FPS测试模式

                    time.sleep(2)
                    continue
                else:
                    print("[RTSP] 连接成功")
                    system_info['rtsp_connected'] = True
                    reconnect_count = 0

            # 读取帧并计算延迟
            frame_start_time = time.time()
            ret, frame = cap.read()
            frame_end_time = time.time()

            if not ret:
                print("[WARNING] 无法读取RTSP帧，尝试重连...")
                cap.release()
                cap = None
                system_info['rtsp_connected'] = False
                system_info['rtsp_latency'] = 0
                continue

            # 计算RTSP通信延迟(毫秒)
            latency_ms = (frame_end_time - frame_start_time) * 1000
            system_info['rtsp_latency'] = round(latency_ms, 1)

            # 更新统计
            stats['frames_received'] += 1

            # 更新原始帧
            with frame_lock:
                current_raw_frame = frame.copy()

            # 放入处理队列
            try:
                frame_queue.put_nowait(frame.copy())
            except:
                # 队列满，清空并放入新帧
                try:
                    while not frame_queue.empty():
                        frame_queue.get_nowait()
                        stats['queue_drops'] += 1
                    frame_queue.put_nowait(frame.copy())
                except:
                    pass

            # 控制帧率
            time.sleep(1.0 / 30)  # 30 FPS

        except Exception as e:
            print(f"[ERROR] RTSP流错误: {e}")
            if cap:
                cap.release()
                cap = None
            system_info['rtsp_connected'] = False
            time.sleep(2)

def create_test_frame():
    """创建测试帧 - 用于RTSP连接失败时的测试模式"""
    frame = np.full((480, 640, 3), 64, dtype=np.uint8)

    # 添加测试信息
    cv2.rectangle(frame, (0, 0), (640, 80), (50, 50, 200), -1)
    cv2.putText(frame, "Test Mode v12", (200, 30),
                cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 255, 255), 2)
    cv2.putText(frame, f"Time: {time.strftime('%H:%M:%S')}", (220, 60),
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

    # 添加一些模拟物体用于测试检测
    cv2.rectangle(frame, (100, 150), (200, 250), (0, 255, 0), 2)
    cv2.putText(frame, "Test Object 1", (105, 145), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

    cv2.rectangle(frame, (400, 200), (500, 300), (255, 0, 0), 2)
    cv2.putText(frame, "Test Object 2", (405, 195), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)

    return frame

def print_stats():
    """打印统计信息线程 - 定期输出系统运行状态"""
    while True:
        time.sleep(30)  # 每30秒打印一次

        elapsed = time.time() - stats['start_time']
        fps_received = stats['frames_received'] / elapsed if elapsed > 0 else 0
        # 处理FPS只在检测启用时才计算
        fps_processed = (stats['frames_processed'] / elapsed if elapsed > 0 else 0) if detection_enabled else 0

        print("\n" + "=" * 70)
        print("📊 RTSP垃圾检测系统统计")
        print("=" * 70)
        print(f"运行时间: {elapsed:.1f}秒 | 设备: {device} | 端口: {system_info['http_port']}")
        print(f"检测状态: {'🟢 启用' if detection_enabled else '🔴 禁用'}")
        print(f"RTSP延迟: {system_info['rtsp_latency']:.1f}ms")
        print(f"RTSP连接: {'🟢 正常' if system_info['rtsp_connected'] else '🔴 断开'}")
        print(f"模型状态: {'🟢 已加载' if system_info['model_loaded'] else '🔴 未加载'}")
        print(f"帧统计: 接收{stats['frames_received']}({fps_received:.1f}FPS) 处理{stats['frames_processed']}({fps_processed:.1f}FPS)")
        print(f"检测统计: 当前{stats['detections_current']}个 总计{stats['detections_total']}个")
        print(f"当前物品: {stats['current_objects']}")
        print(f"推理时间: {stats['model_inference_time']*1000:.1f}ms")
        print(f"有效帧: {stats['valid_frames']} | 错误帧: {stats['error_frames']} | 队列丢弃: {stats['queue_drops']}")
        print("=" * 70 + "\n")

def main():
    """主函数 - 程序入口点"""
    global current_raw_frame, detection_enabled, processing_active, httpd_server, system_info

    print("🚀 启动RTSP垃圾检测控制台")
    print("=" * 60)

    # 注册信号处理器 - 用于程序结束时释放端口
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 初始化系统
    initialize_system()

    # 寻找可用端口
    try:
        port = find_available_port()
        system_info['http_port'] = port
    except Exception as e:
        print(f"[ERROR] 无法找到可用端口: {e}")
        return

    local_ip = system_info['local_ip']
    print(f"[MAIN] Web控制台: http://{local_ip}:{port}")
    print(f"[MAIN] Web端视频: http://{local_ip}:{port}/video_feed")
    print(f"[MAIN] API控制:")
    print(f"    - 启动检测: POST http://{local_ip}:{port}/api/start")
    print(f"    - 停止检测: POST http://{local_ip}:{port}/api/stop")
    print(f"    - 获取状态: POST http://{local_ip}:{port}/api/status")
    print(f"[MAIN] 🚀 多线程优化: 视频流和API请求并发处理，无阻塞！")

    # 启动帧处理线程
    print("[MAIN] 启动帧处理线程...")
    processing_thread = threading.Thread(target=process_frames, daemon=True)
    processing_thread.start()

    # 启动RTSP获取线程
    print("[MAIN] 启动RTSP获取线程...")
    rtsp_thread = threading.Thread(target=rtsp_capture_thread, daemon=True)
    rtsp_thread.start()

    # 启动统计线程
    print("[MAIN] 启动统计线程...")
    stats_thread = threading.Thread(target=print_stats, daemon=True)
    stats_thread.start()

    # 启动多线程HTTP服务器
    print(f"[MAIN] 启动多线程Web服务器在端口 {port}...")
    try:
        # 使用多线程HTTP服务器
        httpd_server = ThreadedHTTPServer(("", port), WebHandler)

        print("✅ 系统启动完成！")
        print("💡 打开浏览器访问Web控制台")
        print("🎯 使用API启动检测后，将返回视频流地址")
        print("🔄 程序结束时将自动释放端口")
        print("🚀 多线程服务器：视频流不会阻塞按钮响应！")

        # 启动服务器
        httpd_server.serve_forever()
    except KeyboardInterrupt:
        print("\n[MAIN] 接收到键盘中断，正在退出...")
        cleanup_and_exit()
    except Exception as e:
        print(f"[ERROR] Web服务器启动失败: {e}")
        cleanup_and_exit()

if __name__ == "__main__":
    main()
