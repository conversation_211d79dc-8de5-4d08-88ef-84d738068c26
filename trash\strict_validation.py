#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
严格验证Web界面按钮功能
模拟浏览器JavaScript的完整请求流程
"""

import requests
import time
import json

API_BASE = "http://*************:5000"

def make_request(endpoint, method="POST", headers=None):
    """发送HTTP请求，模拟浏览器行为"""
    if headers is None:
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache'
        }
    
    url = f"{API_BASE}{endpoint}"
    
    try:
        if method == "POST":
            response = requests.post(url, headers=headers, timeout=10)
        else:
            response = requests.get(url, headers=headers, timeout=10)
        
        print(f"[{method}] {endpoint}")
        print(f"  状态码: {response.status_code}")
        print(f"  响应时间: {response.elapsed.total_seconds():.3f}s")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            data = response.json()
            print(f"  响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            return data
        else:
            print(f"  响应: {response.text[:100]}...")
            return None
            
    except Exception as e:
        print(f"  ❌ 错误: {e}")
        return None

def strict_validation():
    """严格验证流程"""
    print("🔍 严格验证Web界面按钮功能")
    print("=" * 60)
    
    # 1. 获取初始状态
    print("\n1️⃣ 获取初始状态")
    initial_status = make_request("/api/status")
    if not initial_status:
        print("❌ 无法获取初始状态，测试终止")
        return False
    
    initial_frames = initial_status['stats']['frames_processed']
    initial_detection = initial_status['detection_enabled']
    
    print(f"  初始检测状态: {'🟢 启用' if initial_detection else '🔴 禁用'}")
    print(f"  初始处理帧数: {initial_frames}")
    
    # 2. 如果检测已启用，先停止
    if initial_detection:
        print("\n🛑 检测已启用，先停止检测")
        stop_result = make_request("/api/stop")
        if not stop_result or stop_result.get('status') != 'success':
            print("❌ 停止检测失败")
            return False
        time.sleep(2)
    
    # 3. 启动检测测试
    print("\n2️⃣ 启动检测测试")
    start_result = make_request("/api/start")
    
    if not start_result or start_result.get('status') != 'success':
        print("❌ 启动检测API调用失败")
        return False
    
    print("✅ 启动检测API调用成功")
    
    # 4. 验证启动后状态
    print("\n3️⃣ 验证启动后状态")
    time.sleep(1)  # 等待1秒
    after_start_status = make_request("/api/status")
    
    if not after_start_status:
        print("❌ 无法获取启动后状态")
        return False
    
    if not after_start_status['detection_enabled']:
        print("❌ 启动后检测状态仍为禁用")
        return False
    
    if not after_start_status['processing_active']:
        print("❌ 启动后处理状态仍为停止")
        return False
    
    print("✅ 启动后状态验证通过")
    
    # 5. 监控帧数增长
    print("\n4️⃣ 监控帧数增长 (5秒)")
    start_frames = after_start_status['stats']['frames_processed']
    print(f"  启动时处理帧数: {start_frames}")
    
    # 每秒检查一次，共5次
    frame_history = [start_frames]
    for i in range(5):
        time.sleep(1)
        current_status = make_request("/api/status")
        if current_status:
            current_frames = current_status['stats']['frames_processed']
            frame_history.append(current_frames)
            print(f"  第{i+1}秒处理帧数: {current_frames} (增长: {current_frames - start_frames})")
    
    # 验证帧数是否持续增长
    if len(frame_history) < 3:
        print("❌ 无法获取足够的帧数数据")
        return False
    
    growth_count = 0
    for i in range(1, len(frame_history)):
        if frame_history[i] > frame_history[i-1]:
            growth_count += 1
    
    if growth_count < 3:
        print(f"❌ 帧数增长不足，只有{growth_count}次增长")
        return False
    
    print(f"✅ 帧数持续增长验证通过 ({growth_count}次增长)")
    
    # 6. 停止检测测试
    print("\n5️⃣ 停止检测测试")
    stop_result = make_request("/api/stop")
    
    if not stop_result or stop_result.get('status') != 'success':
        print("❌ 停止检测API调用失败")
        return False
    
    print("✅ 停止检测API调用成功")
    
    # 7. 验证停止后状态
    print("\n6️⃣ 验证停止后状态")
    time.sleep(1)
    after_stop_status = make_request("/api/status")
    
    if not after_stop_status:
        print("❌ 无法获取停止后状态")
        return False
    
    if after_stop_status['detection_enabled']:
        print("❌ 停止后检测状态仍为启用")
        return False
    
    if after_stop_status['processing_active']:
        print("❌ 停止后处理状态仍为活跃")
        return False
    
    print("✅ 停止后状态验证通过")
    
    # 8. 验证帧数停止增长
    print("\n7️⃣ 验证帧数停止增长 (3秒)")
    stop_frames = after_stop_status['stats']['frames_processed']
    print(f"  停止时处理帧数: {stop_frames}")
    
    # 等待3秒，检查帧数是否停止增长
    for i in range(3):
        time.sleep(1)
        current_status = make_request("/api/status")
        if current_status:
            current_frames = current_status['stats']['frames_processed']
            growth = current_frames - stop_frames
            print(f"  第{i+1}秒处理帧数: {current_frames} (增长: {growth})")
            
            # 允许少量增长（可能是停止前的缓冲帧）
            if growth > 10:
                print(f"❌ 停止后帧数仍在大量增长: {growth}")
                return False
    
    print("✅ 帧数停止增长验证通过")
    
    # 9. 最终总结
    print("\n8️⃣ 最终验证总结")
    final_status = make_request("/api/status")
    if final_status:
        final_frames = final_status['stats']['frames_processed']
        total_processed = final_frames - initial_frames
        print(f"  测试期间总处理帧数: {total_processed}")
        print(f"  最终检测状态: {'🟢 启用' if final_status['detection_enabled'] else '🔴 禁用'}")
        print(f"  RTSP接收帧数: {final_status['stats']['frames_received']}")
    
    return True

def main():
    """主函数"""
    success = strict_validation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 严格验证通过！Web界面按钮功能完全正常！")
        print("✅ 启动检测按钮有效")
        print("✅ 停止检测按钮有效")
        print("✅ 状态切换正确")
        print("✅ 帧处理控制正常")
    else:
        print("❌ 严格验证失败！按钮功能存在问题！")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
