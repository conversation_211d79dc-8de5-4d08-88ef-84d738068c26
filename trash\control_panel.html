<!DOCTYPE html>
<html>
<head>
    <title>RTSP检测控制面板</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: white;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .control-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .video-section {
            text-align: center;
        }
        .video-container {
            border: 3px solid #fff;
            border-radius: 10px;
            overflow: hidden;
            background: #000;
        }
        #videoStream {
            width: 100%;
            height: auto;
            display: block;
        }
        .controls {
            background: rgba(255,255,255,0.2);
            padding: 20px;
            border-radius: 10px;
        }
        .btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .btn-stop {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }
        .status {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .status-active {
            background: rgba(40, 167, 69, 0.3);
        }
        .status-inactive {
            background: rgba(220, 53, 69, 0.3);
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #ffd700;
        }
        .config {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .config input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: none;
            border-radius: 5px;
            background: rgba(255,255,255,0.9);
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗑️ RTSP垃圾检测控制面板</h1>
        
        <div class="control-panel">
            <div class="video-section">
                <h3>📹 实时视频流</h3>
                <div class="video-container">
                    <img id="videoStream" src="" alt="视频流加载中...">
                </div>
            </div>
            
            <div class="controls">
                <h3>🎛️ 控制面板</h3>
                
                <div id="status" class="status">
                    <strong>状态:</strong> <span id="statusText">检查中...</span>
                </div>
                
                <div>
                    <button class="btn" onclick="startDetection()">🚀 启动检测</button>
                    <button class="btn btn-stop" onclick="stopDetection()">🛑 停止检测</button>
                    <button class="btn" onclick="refreshStatus()">🔄 刷新状态</button>
                </div>
                
                <div class="config">
                    <h4>⚙️ 服务器配置</h4>
                    <input type="text" id="serverIP" placeholder="服务器IP地址" value="*************">
                    <input type="text" id="serverPort" placeholder="端口" value="5000">
                    <button class="btn" onclick="updateServer()">更新服务器</button>
                </div>
            </div>
        </div>
        
        <div class="stats" id="statsContainer">
            <div class="stat-item">
                <div class="stat-value" id="framesReceived">0</div>
                <div>接收帧数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="framesProcessed">0</div>
                <div>处理帧数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="validFrames">0</div>
                <div>有效帧数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="errorFrames">0</div>
                <div>错误帧数</div>
            </div>
        </div>
    </div>
    
    <script>
        let serverIP = '*************';
        let serverPort = '5000';
        let baseURL = `http://${serverIP}:${serverPort}`;
        
        function updateServer() {
            serverIP = document.getElementById('serverIP').value;
            serverPort = document.getElementById('serverPort').value;
            baseURL = `http://${serverIP}:${serverPort}`;
            
            // 更新视频流
            document.getElementById('videoStream').src = `${baseURL}/video_feed?t=${Date.now()}`;
            
            // 刷新状态
            refreshStatus();
            
            alert('服务器配置已更新');
        }
        
        function startDetection() {
            fetch(`${baseURL}/api/start`, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'}
            })
            .then(response => response.json())
            .then(data => {
                alert(data.message);
                refreshStatus();
            })
            .catch(error => {
                alert('启动检测失败: ' + error);
            });
        }
        
        function stopDetection() {
            fetch(`${baseURL}/api/stop`, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'}
            })
            .then(response => response.json())
            .then(data => {
                alert(data.message);
                refreshStatus();
            })
            .catch(error => {
                alert('停止检测失败: ' + error);
            });
        }
        
        function refreshStatus() {
            fetch(`${baseURL}/api/status`, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'}
            })
            .then(response => response.json())
            .then(data => {
                // 更新状态显示
                const statusElement = document.getElementById('status');
                const statusText = document.getElementById('statusText');
                
                if (data.detection_enabled) {
                    statusElement.className = 'status status-active';
                    statusText.textContent = '🟢 检测活跃';
                } else {
                    statusElement.className = 'status status-inactive';
                    statusText.textContent = '🔴 检测停止';
                }
                
                // 更新统计信息
                const stats = data.stats;
                document.getElementById('framesReceived').textContent = stats.frames_received;
                document.getElementById('framesProcessed').textContent = stats.frames_processed;
                document.getElementById('validFrames').textContent = stats.valid_frames;
                document.getElementById('errorFrames').textContent = stats.error_frames;
            })
            .catch(error => {
                document.getElementById('statusText').textContent = '❌ 连接失败';
                console.error('获取状态失败:', error);
            });
        }
        
        // 初始化
        window.onload = function() {
            updateServer();
            
            // 定期刷新状态
            setInterval(refreshStatus, 5000);
        };
    </script>
</body>
</html>
