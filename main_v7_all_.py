#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RTSP流垃圾检测 + HTTP多路推流
结合 i_local.py 和 new_main.py 的功能
- 从RTSP流获取视频
- YOLOv8垃圾检测 (GPU加速)
- HTTP MJPEG多路流推送，支持原始流和处理后流
"""

import cv2
import torch
import numpy as np
from ultralytics import YOLO
from threading import Thread, Lock
from queue import Queue, Empty
from time import time, sleep
import os
import warnings
import socket
import http.server
from functools import partial # Used to pass arguments to the HTTP handler

# Environment configuration to suppress logs
# These environment variables are used to suppress redundant log output from OpenCV and FFmpeg
os.environ['OPENCV_LOG_LEVEL'] = 'SILENT'
os.environ['OPENCV_VIDEOIO_DEBUG'] = '0'
os.environ['FFMPEG_LOG_LEVEL'] = 'quiet'
os.environ['AV_LOG_FORCE_NOCOLOR'] = '1'
os.environ['PYTHONWARNINGS'] = 'ignore' # Corrected: removed extra ']'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

# Further suppress warnings and OpenCV logs
warnings.filterwarnings('ignore')
cv2.setLogLevel(0)

# Device detection and configuration
# Check if CUDA is available, prioritize GPU for PyTorch operations
device = 'cuda' if torch.cuda.is_available() else 'cpu'

# Print environment information to help the user understand the current operating environment
print("=" * 50)
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA device count: {torch.cuda.device_count()}")
    print(f"CUDA device name: {torch.cuda.get_device_name(0)}")
    print(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
print(f"Using device: {device}")
print(f"OpenCV version: {cv2.__version__}")
print("=" * 50)

# Configuration class
# Centralized management of all configurable parameters
class Config:
    # RTSP configuration
    RTSP_URL = "rtsp://192.168.0.153:8554/camera" # Your RTSP video stream URL
    RTSP_RECONNECT_MAX = 5 # Maximum number of RTSP stream reconnections
    RTSP_BUFFER_SIZE = 1 # OpenCV video capture buffer size, set to 1 to reduce latency

    # Detection configuration
    CONFIDENCE_THRESHOLD = 0.5 # Confidence threshold for YOLO detection
    # GARBAGE_CLASSES will be dynamically set to model.names after model initialization
    # It will include all categories supported by the loaded YOLOv8 model.
    GARBAGE_CLASSES = {0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane', 5: 'bus', 6: 'train', 7: 'truck', 8: 'boat', 9: 'traffic light', 10: 'fire hydrant', 11: 'stop sign', 12: 'parking meter', 13: 'bench', 14: 'bird', 15: 'cat', 16: 'dog', 17: 'horse', 18: 'sheep', 19: 'cow', 20: 'elephant', 21: 'bear', 22: 'zebra', 23: 'giraffe', 24: 'backpack', 25: 'umbrella', 26: 'handbag', 27: 'tie', 29: 'frisbee', 30: 'skis', 31: 'snowboard', 32: 'sports ball', 33: 'kite', 34: 'baseball bat', 35: 'baseball glove', 36: 'skateboard', 37: 'surfboard', 38: 'tennis racket', 39: 'bottle', 40: 'wine glass', 41: 'cup', 42: 'fork', 43: 'knife', 44: 'spoon', 45: 'bowl', 46: 'banana', 47: 'apple', 48: 'sandwich', 49: 'orange', 50: 'broccoli', 51: 'carrot', 52: 'hot dog', 53: 'pizza', 54: 'donut', 55: 'cake', 56: 'chair', 57: 'couch', 58: 'potted plant', 59: 'bed', 60: 'dining table',  62: 'tv', 63: 'laptop', 64: 'mouse', 65: 'remote', 66: 'keyboard', 67: 'cell phone', 68: 'microwave', 69: 'oven', 70: 'toaster', 71: 'sink', 72: 'refrigerator', 73: 'book', 74: 'clock', 75: 'vase', 76: 'scissors', 77: 'teddy bear', 78: 'hair drier', 79: 'toothbrush'}
     # Placeholder, will be populated dynamically
    MODEL_PATH = 'yolov8n.pt' # Path to the YOLOv8 model file, ensure it exists in the same directory as the script

    # Performance configuration
    QUEUE_SIZE = 3  # Frame processing queue size, reducing it can lower latency but may increase frame drops
    STATS_INTERVAL = 30  # Statistics printing interval (seconds)
    HTTP_FPS = 20  # HTTP MJPEG stream push frame rate

    # HTTP configuration - Define multiple video streams, each with a different port and source type
    # "source": "processed" means frames that have undergone YOLO detection and drawing
    # "source": "raw" means raw frames directly from the RTSP stream, without processing
    STREAM_CONFIGS = [
        {"name": "Detection Result Stream", "port": 5000, "path": "/video_feed", "source": "processed"},
        {"name": "Raw Camera Stream", "port": 5001, "path": "/video_feed", "source": "raw"}
    ]
    ENABLE_DISPLAY = False  # Whether to enable OpenCV window display (requires a graphical environment)

# Backward compatibility (for consistency with old code snippets, but direct use of Config class is recommended)
# These will be updated dynamically after model loading
garbage_classes = {}
CONFIDENCE_THRESHOLD = Config.CONFIDENCE_THRESHOLD
QUEUE_SIZE = Config.QUEUE_SIZE
STATS_INTERVAL = Config.STATS_INTERVAL

# Model initialization
# Load the YOLOv8 model
model = YOLO(Config.MODEL_PATH)

# Move the model to the specified device (GPU or CPU)
if device == 'cuda':
    model.to(device)
    print(f"[DEBUG] Model loaded to GPU: {device}")
else:
    print(f"[DEBUG] Model loaded to CPU: {device}")

print("[DEBUG] Model loaded successfully")
# Dynamically set GARBAGE_CLASSES to all supported mo del names
Config.GARBAGE_CLASSES = model.names
Config.GARBAGE_CLASSES ={0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane', 5: 'bus', 6: 'train', 7: 'truck', 8: 'boat', 9: 'traffic light', 10: 'fire hydrant', 11: 'stop sign', 12: 'parking meter', 13: 'bench', 14: 'bird', 15: 'cat', 16: 'dog', 17: 'horse', 18: 'sheep', 19: 'cow', 20: 'elephant', 21: 'bear', 22: 'zebra', 23: 'giraffe', 24: 'backpack', 25: 'umbrella', 26: 'handbag', 27: 'tie', 29: 'frisbee', 30: 'skis', 31: 'snowboard', 32: 'sports ball', 33: 'kite', 34: 'baseball bat', 35: 'baseball glove', 36: 'skateboard', 37: 'surfboard', 38: 'tennis racket', 39: 'bottle', 40: 'wine glass', 41: 'cup', 42: 'fork', 43: 'knife', 44: 'spoon', 45: 'bowl', 46: 'banana', 47: 'apple', 48: 'sandwich', 49: 'orange', 50: 'broccoli', 51: 'carrot', 52: 'hot dog', 53: 'pizza', 54: 'donut', 55: 'cake', 56: 'chair', 57: 'couch', 58: 'potted plant', 59: 'bed', 60: 'dining table',  62: 'tv', 63: 'laptop', 64: 'mouse', 65: 'remote', 66: 'keyboard', 67: 'cell phone', 68: 'microwave', 69: 'oven', 70: 'toaster', 71: 'sink', 72: 'refrigerator', 73: 'book', 74: 'clock', 75: 'vase', 76: 'scissors', 77: 'teddy bear', 78: 'hair drier', 79: 'toothbrush'}
# Update the global variable for backward compatibility
# Removed 'global garbage_classes' as it's not needed when assigning at the module level
garbage_classes = Config.GARBAGE_CLASSES

# Print all categories supported by the model
# This will show all categories (e.g., from yolov8n.pt) and their ID mappings
print(f"[INFO] All categories supported by the model: {Config.GARBAGE_CLASSES}")


# Global variables
# Used to store the latest processed frame and raw frame, protected by a lock for concurrent access
current_processed_frame = None
current_raw_frame = None
frame_lock = Lock() # Mutex lock protecting global frame variables
frame_queue = Queue(maxsize=QUEUE_SIZE) # Queue for frames awaiting YOLO processing

# Statistics dictionary
stats = {
    'frames_received': 0, # Total frames received from RTSP
    'frames_processed': 0, # Frames processed by YOLO
    'frames_displayed': 0, # Frames displayed in the OpenCV window
    'valid_frames': 0, # Frames deemed valid and entered the processing pipeline
    'error_frames': 0, # Number of invalid frames detected
    'queue_drops': 0, # Frames dropped due to full queue
    'start_time': time() # Program start time
}

def get_local_ip():
    """
    Get the local IP address on the LAN.
    Attempts to connect to a public DNS server (without sending data) to get the local IP.
    If unsuccessful, returns the localhost IP as a fallback.
    """
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.settimeout(2) # Set timeout
        s.connect(("*******", 80)) # Try to connect to a public DNS to get the local outgoing IP
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception as e:
        print(f"[WARNING] Failed to get IP: {e}, using default IP address 127.0.0.1")
        return "127.0.0.1" # Use loopback address if IP cannot be obtained

def create_test_frame():
    """
    Create a test frame.
    The HTTP server will push this test frame when the RTSP stream is unavailable.
    """
    frame = np.zeros((480, 640, 3), dtype=np.uint8) # Create a black background frame
    
    # Add hint text
    cv2.putText(frame, "RTSP Stream Not Available", (150, 200), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    cv2.putText(frame, "Showing Test Pattern", (200, 250), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
    cv2.putText(frame, f"Time: {int(time())}", (250, 300), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    # Add simple graphics
    cv2.circle(frame, (320, 350), 50, (0, 0, 255), 3) # Red circle
    cv2.rectangle(frame, (250, 400), (390, 450), (255, 0, 0), 3) # Blue rectangle
    
    return frame

def is_frame_valid(frame):
    """
    Check if the captured frame is valid.
    Conditions include: not None, not empty, 3-channel color image, sufficient size,
    and pixel mean value not at extreme ends (e.g., completely black or white).
    """
    if frame is None or frame.size == 0:
        return False
    if len(frame.shape) != 3 or frame.shape[2] != 3: # Check if it's a 3-channel color image
        return False
    if frame.shape[0] < 100 or frame.shape[1] < 100: # Check if frame size is too small
        return False
    mean_val = np.mean(frame)
    if mean_val < 10 or mean_val > 245: # Check if it's a completely black or white frame
        return False
    return True

def draw_detections(frame, detections):
    """
    Draw bounding boxes and labels for YOLO detections on the frame.
    """
    for x1, y1, x2, y2, label in detections:
        # Draw green bounding box
        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        
        # Draw label background (solid green rectangle)
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
        cv2.rectangle(frame, (x1, y1 - label_size[1] - 10), 
                      (x1 + label_size[0], y1), (0, 255, 0), -1)
        
        # Draw label text (black)
        cv2.putText(frame, label, (x1, y1 - 5), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    
    return frame

class DynamicStreamingHandler(http.server.BaseHTTPRequestHandler):
    """
    HTTP streaming handler.
    Determines whether to provide 'processed' frames or 'raw' frames via the 'source_type' constructor parameter.
    """
    def __init__(self, *args, source_type, **kwargs):
        self.source_type = source_type # 'processed' or 'raw'
        super().__init__(*args, **kwargs)

    def do_GET(self):
        global current_processed_frame, current_raw_frame
        
        if self.path == '/':
            # Main page - Lists links to all configured video streams for team members to choose from
            self.send_response(200)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.end_headers()
            
            local_ip = get_local_ip()
            stream_links_html = ""
            # Iterate through each video stream configuration defined in Config to generate HTML links
            for stream_cfg in Config.STREAM_CONFIGS:
                stream_url = f"http://{local_ip}:{stream_cfg['port']}{stream_cfg['path']}"
                stream_home_url = f"http://{local_ip}:{stream_cfg['port']}/"
                stream_links_html += f"""
                <div class="stream-card">
                    <h3>{stream_cfg['name']}</h3>
                    <p>Port: {stream_cfg['port']}</p>
                    <p>Source Type: {stream_cfg['source'].capitalize()}</p>
                    <p><a href="{stream_url}" target="_blank">Click here to view video stream (new window)</a></p>
                    <p><a href="{stream_home_url}" target="_self">Return to this port's homepage</a></p>
                    <img src="{stream_url}" alt="{stream_cfg['name']}" style="max-width: 90%; border-radius: 5px;">
                </div>
                """

            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>RTSP Garbage Detection Multi-Stream Server</title>
                <style>
                    body {{ font-family: 'Inter', Arial, sans-serif; text-align: center; background: #f0f2f5; color: #333; margin: 0; padding: 20px; }}
                    .container {{ max-width: 1200px; margin: 20px auto; padding: 30px; background: #ffffff; border-radius: 12px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); }}
                    h1 {{ color: #2c3e50; margin-bottom: 25px; font-size: 2.5em; }}
                    .info {{ background: #e9f7ef; color: #28a745; padding: 15px; margin: 20px 0; border-radius: 8px; border: 1px solid #d4edda; font-size: 1.1em; }}
                    .info p {{ margin: 5px 0; }}
                    h2 {{ color: #34495e; margin-top: 40px; margin-bottom: 20px; font-size: 2em; }}
                    .stream-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 30px; margin-top: 30px; }}
                    .stream-card {{ background: #fdfdfd; border: 1px solid #e0e0e0; border-radius: 10px; padding: 20px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05); text-align: left; }}
                    .stream-card h3 {{ color: #007bff; margin-top: 0; font-size: 1.5em; }}
                    .stream-card p {{ margin: 8px 0; font-size: 1em; }}
                    .stream-card a {{ color: #007bff; text-decoration: none; font-weight: bold; }}
                    .stream-card a:hover {{ text-decoration: underline; }}
                    img {{ border: 2px solid #ddd; border-radius: 8px; max-width: 100%; height: auto; display: block; margin: 15px auto 0; }}
                    .tip {{ margin-top: 30px; font-style: italic; color: #666; font-size: 0.9em; }}
                    @media (max-width: 768px) {{
                        .stream-grid {{ grid-template-columns: 1fr; }}
                        .container {{ padding: 20px; }}
                    }}
                </style>
                <script src="https://cdn.tailwindcss.com"></script>
            </head>
            <body>
                <div class="container">
                    <h1>🗑️ RTSP Garbage Detection Multi-Stream Server</h1>
                    <div class="info">
                        <p>Real-time detection targets: All available COCO classes</p>
                        <p>RTSP Source: {Config.RTSP_URL}</p>
                        <p>Local IP Address: {local_ip}</p>
                    </div>
                    <h2>Available Video Streams:</h2>
                    <div class="stream-grid">
                        {stream_links_html}
                    </div>
                    <p class="tip">💡 Tip: Each video stream runs on a separate HTTP service port.</p>
                </div>
            </body>
            </html>
            """
            self.wfile.write(html.encode())
            
        elif self.path == '/video_feed':
            # MJPEG stream push
            self.send_response(200)
            self.send_header('Content-Type', 'multipart/x-mixed-replace; boundary=frame')
            self.send_header('Cache-Control', 'no-cache')
            self.send_header('Connection', 'close') # Close connection to force browser to re-request MJPEG
            self.end_headers()
            
            frame_count = 0
            while True:
                try:
                    frame_to_send = None
                    with frame_lock: # Lock to safely access global frame variables
                        if self.source_type == "processed":
                            frame_to_send = current_processed_frame.copy() if current_processed_frame is not None else None
                        elif self.source_type == "raw":
                            frame_to_send = current_raw_frame.copy() if current_raw_frame is not None else None
                    
                    # If no frame is available (e.g., RTSP stream not connected), push a test frame
                    if frame_to_send is None:
                        frame_to_send = create_test_frame()
                        cv2.putText(frame_to_send, f"Stream Type: {self.source_type.capitalize()} | Frame: {frame_count}", (20, 60), 
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 2)
                    
                    # Encode the frame as JPEG
                    ret, buffer = cv2.imencode('.jpg', frame_to_send, 
                                                 [cv2.IMWRITE_JPEG_QUALITY, 85]) # 85% JPEG quality
                    if ret:
                        frame_bytes = buffer.tobytes()
                        
                        # Construct the HTTP response body for MJPEG stream
                        self.wfile.write(b'--frame\r\n')
                        self.wfile.write(b'Content-Type: image/jpeg\r\n')
                        self.wfile.write(f'Content-Length: {len(frame_bytes)}\r\n\r\n'.encode())
                        self.wfile.write(frame_bytes)
                        self.wfile.write(b'\r\n')
                        self.wfile.flush() # Send data immediately
                        
                        frame_count += 1
                        
                    sleep(1.0 / Config.HTTP_FPS) # Control stream frame rate based on configuration

                except ConnectionResetError:
                    # Client disconnected, this is a normal termination for MJPEG streams
                    break
                except BrokenPipeError:
                    # Pipe broken, also a normal client disconnection
                    break
                except Exception as e:
                    # Catch and print other exceptions, avoiding frequent connection interruption error messages
                    if "connection" not in str(e).lower() and "winerror 10038" not in str(e).lower(): # 10038 is a typical socket close error on Windows
                        print(f"[HTTP] Stream transmission error ({self.source_type}): {e}")
                    break
        else:
            # Handle unknown path requests
            self.send_response(404)
            self.end_headers()

    def log_message(self, format, *args):
        # Suppress HTTP server access logs to keep console output clean
        _ = format, args
        pass

def process_frames():
    """
    Independent thread function responsible for getting frames from the queue, performing YOLO detection,
    and updating the global 'processed' frame.
    """
    global current_processed_frame

    while True:
        try:
            # Get the frame to be processed from the queue, with a timeout
            frame = frame_queue.get(timeout=5)

            if not is_frame_valid(frame):
                stats['error_frames'] += 1
                continue

            stats['valid_frames'] += 1

            # YOLO detection (using the configured device, imgsz=416 provides faster inference)
            results = model(frame, imgsz=416, conf=CONFIDENCE_THRESHOLD, verbose=False, device=device)[0]
            detections = []

            # Parse YOLO detection results
            for box in results.boxes:
                class_id = int(box.cls)
                # Now, it will consider all classes available in model.names for detection
                if class_id in Config.GARBAGE_CLASSES: # Check if class_id is in the dynamically loaded names
                #if class_id in GARBAGE_CLASSES: # Check if class_id is in the dynamically loaded names
                    x1, y1, x2, y2 = map(int, box.xyxy[0]) # Bounding box coordinates
                    confidence = float(box.conf) # Confidence score
                    label = f"{Config.GARBAGE_CLASSES[class_id]} {confidence:.2f}" # Label text
                    detections.append((x1, y1, x2, y2, label))

            # Draw detection results on the frame
            processed_frame = draw_detections(frame.copy(), detections)

            # Add info text to the frame
            cv2.putText(processed_frame, f"Objects: {len(detections)} | Device: {device}",
                        (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            cv2.putText(processed_frame, f"IP: {get_local_ip()}",
                        (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

            # Update the global processed frame, protected by a lock
            with frame_lock:
                current_processed_frame = processed_frame

            stats['frames_processed'] += 1

        except Empty:
            print("[WARNING] Frame processing thread timed out waiting for queue, no frames to process.")
            continue
        except Exception as e:
            print(f"[PROCESS_THREAD] Frame processing thread exception: {e}")
            break

    print("[PROCESS_THREAD] Frame processing thread exited.")

def start_http_server(port, handler_class, server_name="HTTP Server"):
    """
    Independent function to start the HTTP streaming server.
    Each HTTP server runs in a separate thread.
    """
    try:
        from http.server import HTTPServer
        # Bind to all network interfaces (0.0.0.0) so that other devices on the LAN can access it
        httpd = HTTPServer(("0.0.0.0", port), handler_class)
        local_ip = get_local_ip()
        print(f"[{server_name}] Server starting on port {port}...")
        print(f"[{server_name}] Local access: http://localhost:{port}/")
        print(f"[{server_name}] LAN access: http://{local_ip}:{port}/")
        print(f"[{server_name}] Direct video stream URL: http://{local_ip}:{port}/video_feed")

        # Set server timeout to prevent infinite blocking when there are no requests
        httpd.timeout = 1.0

        # Start the server request handling loop
        print(f"[{server_name}] Server listening...")
        while True:
            try:
                httpd.handle_request() # Handle a single HTTP request
            except KeyboardInterrupt:
                print(f"\n[{server_name}] Received exit signal, server stopping.")
                break
            except Exception as e:
                # Suppress common connection interruption errors, only print other critical errors
                if "connection" not in str(e).lower() and "winerror 10038" not in str(e).lower():
                    print(f"[{server_name}] Request processing error: {e}")
                continue # Continue listening for the next request

    except Exception as e:
        print(f"[{server_name}] Server failed to start: {e}")
        import traceback
        traceback.print_exc() # Print full exception traceback

def print_stats():
    """
    Periodically print program runtime statistics, including frame rate, memory usage, etc.
    """
    elapsed = time() - stats['start_time']
    fps_received = stats['frames_received'] / elapsed if elapsed > 0 else 0
    fps_processed = stats['frames_processed'] / elapsed if elapsed > 0 else 0
    fps_displayed = stats['frames_displayed'] / elapsed if elapsed > 0 else 0
    valid_rate = (stats['valid_frames'] / max(stats['frames_received'], 1)) * 100

    print("\n" + "=" * 60)
    print("📊 Running Statistics")
    print("=" * 60)
    print(f"Runtime: {elapsed:.1f} seconds")
    print(f"Device: {device}")

    # Print GPU memory usage (if CUDA is used)
    if device == 'cuda' and torch.cuda.is_available():
        gpu_memory_used = torch.cuda.memory_allocated(0) / 1024**3
        gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / 1024**3
        gpu_utilization = (gpu_memory_used / gpu_memory_total) * 100
        print(f"GPU Memory: {gpu_memory_used:.2f}GB / {gpu_memory_total:.1f}GB ({gpu_utilization:.1f}%)")

    print(f"Frames Received: {stats['frames_received']} ({fps_received:.1f} FPS)")
    print(f"Valid Frames: {stats['valid_frames']} (Validity Rate: {valid_rate:.1f}%)")
    print(f"Frames Processed: {stats['frames_processed']} ({fps_processed:.1f} FPS)")
    print(f"Frames Displayed: {stats['frames_displayed']} ({fps_displayed:.1f} FPS)")
    print(f"Queue Drops: {stats['queue_drops']}")
    print("=" * 60 + "\n")

def main():
    """
    Main function, responsible for initialization, starting threads, handling RTSP streams,
    and managing the program's lifecycle.
    """
    global current_raw_frame

    # Get local IP address and print
    local_ip = get_local_ip()
    print(f"[INFO] Local IP Address: {local_ip}")

    # Start frame processing thread (daemon=True ensures thread terminates automatically when main program exits)
    Thread(target=process_frames, daemon=True).start()
    print("[THREAD] Frame processing thread started.")

    # Start HTTP streaming servers (each configured stream in a separate thread)
    http_server_threads = []
    for stream_cfg in Config.STREAM_CONFIGS:
        # Use functools.partial to create the HTTP handler, binding the source_type parameter to DynamicStreamingHandler
        handler_with_args = partial(DynamicStreamingHandler, source_type=stream_cfg['source'])
        
        http_thread = Thread(target=start_http_server, 
                             args=(stream_cfg['port'], handler_with_args, stream_cfg['name']))
        http_thread.daemon = True # Set as daemon thread, exits with main program
        http_server_threads.append(http_thread)
        http_thread.start()
        sleep(0.5) # Small delay to ensure server startup order

    print(f"[MAIN] Starting RTSP stream garbage detection application.")
    print(f"[MAIN] RTSP Source: {Config.RTSP_URL}")
    print(f"[MAIN] 🚀 Running on Device: {device}")
    if device == 'cuda':
        print(f"[MAIN] 🎮 GPU Acceleration: Enabled")
    else:
        print(f"[MAIN] 💻 CPU Mode: Enabled")
    print(f"[MAIN] 💡 If OpenCV display window is enabled, press 'q' to quit, 's' to save screenshot.")
    print(f"[MAIN] 🌐 Available HTTP Video Streams:")
    for stream_cfg in Config.STREAM_CONFIGS:
        print(f"    - {stream_cfg['name']}: http://{local_ip}:{stream_cfg['port']}/")
    print(f"[MAIN] 🌐 LAN Access: Open any of the above addresses in a browser.")

    # Main loop variables
    fps_counter = 0
    fps_timer = time()
    last_stats_time = time()

    try:
        # Open RTSP stream
        print(f"[RTSP] Attempting to connect to RTSP stream: {Config.RTSP_URL}")
        cap = cv2.VideoCapture(Config.RTSP_URL)

        # Check if RTSP stream opened successfully
        if not cap.isOpened():
            print(f"[ERROR] Unable to open RTSP stream: {Config.RTSP_URL}")
            print("[INFO] Running HTTP server in test mode (displaying test image).")
            # Even if RTSP stream fails, keep HTTP server running to provide test frames
            try:
                while True:
                    sleep(1) # Pause for 1 second to avoid high CPU usage
                    # Periodically print statistics
                    if time() - last_stats_time > STATS_INTERVAL:
                        print_stats()
                        last_stats_time = time()
            except KeyboardInterrupt:
                print("\n[MAIN] Received exit signal, program exiting.")
            return # Exit main function

        print(f"[RTSP] RTSP stream connected successfully.")

        # Optimize RTSP settings
        cap.set(cv2.CAP_PROP_BUFFERSIZE, Config.RTSP_BUFFER_SIZE) # Minimize buffer to reduce latency
        # cap.set(cv2.CAP_PROP_FPS, 30) # Attempt to set frame rate, but camera may not support it

        # Reconnection attempt counter
        reconnect_count = 0
        max_reconnects = Config.RTSP_RECONNECT_MAX

        # Main loop: Read RTSP frames, put into queue, and update global raw frame
        while True:
            ret, frame = cap.read()

            if not ret: # If frame cannot be read, attempt to reconnect
                reconnect_count += 1
                print(f"[WARNING] Unable to read RTSP frame, attempting reconnect... ({reconnect_count}/{max_reconnects})")

                cap.release() # Release current capture object
                sleep(min(reconnect_count * 2, 10)) # Incremental delay, max 10 seconds

                cap = cv2.VideoCapture(Config.RTSP_URL) # Recreate capture object
                if not cap.isOpened():
                    if reconnect_count >= max_reconnects:
                        print(f"[ERROR] RTSP reconnection failed, maximum retry attempts ({max_reconnects}) reached. Exiting program.")
                        break # Exceeded max retries, exit main loop
                    continue # Continue to next retry
                else:
                    print("[INFO] RTSP reconnected successfully.")
                    reconnect_count = 0 # Reset counter
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, Config.RTSP_BUFFER_SIZE) # Reset buffer
                continue

            # If frame read successfully, reset reconnect count
            if reconnect_count > 0:
                reconnect_count = 0

            stats['frames_received'] += 1

            # Update global raw frame (protected by lock)
            with frame_lock:
                current_raw_frame = frame.copy() # Store a copy for raw stream

            # Smart queue management: Put frame into processing queue
            try:
                frame_queue.put_nowait(frame.copy()) # Put a copy of the frame for YOLO processing
            except:
                # If queue is full, clear the queue and put the new frame to ensure the latest frame is always processed
                try:
                    while not frame_queue.empty():
                        frame_queue.get_nowait()
                        stats['queue_drops'] += 1 # Record dropped frames
                    frame_queue.put_nowait(frame.copy())
                except Exception as e:
                    print(f"[WARNING] Queue management error: {e}")

            # Optional OpenCV window display (based on Config.ENABLE_DISPLAY)
            try:
                if Config.ENABLE_DISPLAY:
                    display_frame = None
                    with frame_lock:
                        if current_processed_frame is not None:
                            display_frame = current_processed_frame.copy()
                        elif current_raw_frame is not None:
                            # If processed frame is not available, fall back to displaying raw frame
                            display_frame = current_raw_frame.copy()

                    if display_frame is not None:
                        cv2.imshow("RTSP Garbage Detection", display_frame)
                        stats['frames_displayed'] += 1

                        # Handle key presses
                        key = cv2.waitKey(1) & 0xFF
                        if key == ord('q'):
                            print("[MAIN] User requested exit.")
                            break # User pressed 'q' to exit
                        elif key == ord('s'):
                            # Press 's' to save screenshot
                            if current_processed_frame is not None:
                                filename = f"screenshot_processed_{int(time())}.jpg"
                                cv2.imwrite(filename, current_processed_frame)
                                print(f"[MAIN] Processed screenshot saved: {filename}")
                            elif current_raw_frame is not None:
                                filename = f"screenshot_raw_{int(time())}.jpg"
                                cv2.imwrite(filename, current_raw_frame)
                                print(f"[MAIN] Raw screenshot saved: {filename}")
                else:
                    # In non-GUI environments, sleep to reduce CPU usage
                    sleep(0.01)
            except Exception as e:
                # Catch display-related errors, which usually don't affect core functionality
                if "cannot connect to X server" not in str(e).lower() and "noatime" not in str(e).lower():
                    print(f"[WARNING] Display error: {e}")
                sleep(0.01) # Even with errors, pause briefly to avoid infinite loop high CPU usage

            # FPS calculation
            fps_counter += 1
            if time() - fps_timer > 1.0:
                fps_timer = time()
                fps_counter = 0

            # Periodically print statistics
            if time() - last_stats_time > STATS_INTERVAL:
                print_stats()
                last_stats_time = time()

    except KeyboardInterrupt:
        print("\n[MAIN] Received exit signal, program shutting down...")
    except Exception as e:
        print(f"[ERROR] Main loop exception: {e}")
        import traceback
        traceback.print_exc() # Print traceback for exceptions in the main loop
    finally:
        # Clean up resources
        try:
            if 'cap' in locals() and cap.isOpened():
                cap.release() # Release video capture object
        except:
            pass
        cv2.destroyAllWindows() # Close all OpenCV windows

        # Final statistics print
        print_stats()
        print("[MAIN] Program exited.")

if __name__ == "__main__":
    main()
